// Prevents additional console window on Windows in release, DO NOT REMOVE!!
#![cfg_attr(not(debug_assertions), windows_subsystem = "windows")]

use tauri::{Manager, Window};
use std::sync::{Arc, Mutex};
use rusqlite::{Connection, Result as SqlResult};
use chrono::{DateTime, Utc};
use serde::{Deserialize, Serialize};
use std::fs;
use std::path::PathBuf;

// Audio state management für Future Verwendung
struct AudioState {
    is_recording: bool,
    audio_data: Vec<f32>,
}

type AudioStateArc = Arc<Mutex<AudioState>>;

// Recording-Historie Datenstrukturen
#[derive(Debug, Serialize, Deserialize, Clone)]
struct Recording {
    id: i64,
    timestamp: DateTime<Utc>,
    duration_seconds: f64,
    transcription: String,
    cost_usd: f64,
    file_path: Option<String>,
}

#[derive(Debug, Serialize, Deserialize)]
struct RecordingInput {
    #[serde(alias = "durationSeconds")]
    duration_seconds: f64,
    transcription: String,
    #[serde(alias = "costUsd")]
    cost_usd: f64,
    #[serde(alias = "filePath")]
    file_path: Option<String>,
}

// App-Einstellungen
#[derive(Debug, Serialize, Deserialize, Clone)]
struct AppSettings {
    openai_api_key: Option<String>,
    auto_clipboard: bool,
    language: String,
}

impl Default for AppSettings {
    fn default() -> Self {
        Self {
            openai_api_key: None,
            auto_clipboard: true,
            language: "de".to_string(),
        }
    }
}

#[tauri::command]
async fn start_native_recording(_state: tauri::State<'_, AudioStateArc>) -> Result<String, String> {
    println!("🎤 Native audio recording start requested");
    
    // Plugin-basierte Aufnahme wird über Frontend gesteuert
    Ok("Native recording ready - use plugin commands".to_string())
}

#[tauri::command]
async fn stop_native_recording(_state: tauri::State<'_, AudioStateArc>) -> Result<String, String> {
    println!("🛑 Native audio recording stop requested");
    
    // Plugin-basierte Aufnahme wird über Frontend gesteuert
    Ok("Native recording stopped - use plugin commands".to_string())
}

#[tauri::command]
async fn check_microphone_access() -> Result<String, String> {
    println!("🔍 Checking microphone access...");
    
    // Plugin wird Berechtigung automatisch anfragen
    Ok("Microphone access will be requested by plugin".to_string())
}

#[tauri::command]
async fn start_window_drag(window: Window) -> Result<(), String> {
    window.start_dragging().map_err(|e| e.to_string())
}

// Datenbank-Funktionen
fn init_database() -> SqlResult<Connection> {
    let conn = Connection::open("recordings.db")?;

    conn.execute(
        "CREATE TABLE IF NOT EXISTS recordings (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            timestamp TEXT NOT NULL,
            duration_seconds REAL NOT NULL,
            transcription TEXT NOT NULL,
            cost_usd REAL NOT NULL,
            file_path TEXT
        )",
        [],
    )?;

    println!("✅ Database initialized");
    Ok(conn)
}

#[tauri::command]
async fn save_recording(input: RecordingInput) -> Result<i64, String> {
    println!("💾 Saving recording to database...");
    println!("📊 Duration: {}s, Cost: ${}, Text: {}", input.duration_seconds, input.cost_usd, input.transcription.chars().take(50).collect::<String>());

    let conn = init_database().map_err(|e| format!("Database error: {}", e))?;

    let timestamp = Utc::now();

    let result = conn.execute(
        "INSERT INTO recordings (timestamp, duration_seconds, transcription, cost_usd, file_path)
         VALUES (?1, ?2, ?3, ?4, ?5)",
        [
            &timestamp.to_rfc3339(),
            &input.duration_seconds.to_string(),
            &input.transcription,
            &input.cost_usd.to_string(),
            &input.file_path.unwrap_or_default(),
        ],
    );

    match result {
        Ok(_) => {
            let id = conn.last_insert_rowid();
            println!("✅ Recording saved with ID: {}", id);
            Ok(id)
        }
        Err(e) => {
            println!("❌ Failed to save recording: {}", e);
            Err(format!("Failed to save recording: {}", e))
        }
    }
}

#[tauri::command]
async fn get_recordings_history(limit: Option<i32>) -> Result<Vec<Recording>, String> {
    println!("📋 Getting recordings history...");

    let conn = init_database().map_err(|e| format!("Database error: {}", e))?;
    let limit = limit.unwrap_or(10);

    let mut stmt = conn.prepare(
        "SELECT id, timestamp, duration_seconds, transcription, cost_usd, file_path
         FROM recordings
         ORDER BY timestamp DESC
         LIMIT ?1"
    ).map_err(|e| format!("Prepare error: {}", e))?;

    let recording_iter = stmt.query_map([limit], |row| {
        let timestamp_str: String = row.get(1)?;
        let timestamp = DateTime::parse_from_rfc3339(&timestamp_str)
            .map_err(|_| rusqlite::Error::InvalidColumnType(1, "timestamp".to_string(), rusqlite::types::Type::Text))?
            .with_timezone(&Utc);

        Ok(Recording {
            id: row.get(0)?,
            timestamp,
            duration_seconds: row.get(2)?,
            transcription: row.get(3)?,
            cost_usd: row.get(4)?,
            file_path: row.get(5)?,
        })
    }).map_err(|e| format!("Query error: {}", e))?;

    let mut recordings = Vec::new();
    for recording in recording_iter {
        recordings.push(recording.map_err(|e| format!("Row error: {}", e))?);
    }

    println!("✅ Retrieved {} recordings", recordings.len());
    Ok(recordings)
}

#[tauri::command]
async fn delete_recording(id: i64) -> Result<(), String> {
    println!("🗑️ Deleting recording with ID: {}", id);

    let conn = init_database().map_err(|e| format!("Database error: {}", e))?;

    let result = conn.execute("DELETE FROM recordings WHERE id = ?1", [id]);

    match result {
        Ok(rows_affected) => {
            if rows_affected > 0 {
                println!("✅ Recording deleted successfully");
                Ok(())
            } else {
                Err("Recording not found".to_string())
            }
        }
        Err(e) => {
            println!("❌ Failed to delete recording: {}", e);
            Err(format!("Failed to delete recording: {}", e))
        }
    }
}

// Einstellungen-Funktionen
fn get_settings_path() -> Result<PathBuf, String> {
    let mut path = std::env::current_dir()
        .map_err(|e| format!("Failed to get current directory: {}", e))?;
    path.push("app_data");
    path.push("settings.json");
    Ok(path)
}

#[tauri::command]
async fn save_settings(settings: AppSettings) -> Result<(), String> {
    println!("💾 Saving app settings...");

    let settings_path = get_settings_path()?;

    // Erstelle Verzeichnis falls es nicht existiert
    if let Some(parent) = settings_path.parent() {
        fs::create_dir_all(parent).map_err(|e| format!("Failed to create settings directory: {}", e))?;
    }

    let settings_json = serde_json::to_string_pretty(&settings)
        .map_err(|e| format!("Failed to serialize settings: {}", e))?;

    fs::write(&settings_path, settings_json)
        .map_err(|e| format!("Failed to write settings file: {}", e))?;

    println!("✅ Settings saved to: {:?}", settings_path);
    Ok(())
}

#[tauri::command]
async fn load_settings() -> Result<AppSettings, String> {
    println!("📋 Loading app settings...");

    let settings_path = get_settings_path()?;

    if !settings_path.exists() {
        println!("⚠️ Settings file not found, using defaults");
        return Ok(AppSettings::default());
    }

    let settings_content = fs::read_to_string(&settings_path)
        .map_err(|e| format!("Failed to read settings file: {}", e))?;

    let settings: AppSettings = serde_json::from_str(&settings_content)
        .map_err(|e| format!("Failed to parse settings: {}", e))?;

    println!("✅ Settings loaded from: {:?}", settings_path);
    Ok(settings)
}

#[tauri::command]
async fn read_audio_file(file_path: String) -> Result<Vec<u8>, String> {
    println!("📁 Reading audio file: {}", file_path);

    // Prüfe ob die Datei existiert
    if !std::path::Path::new(&file_path).exists() {
        return Err(format!("Audio file not found: {}", file_path));
    }

    match std::fs::read(&file_path) {
        Ok(data) => {
            println!("✅ Audio file read successfully, size: {} bytes", data.len());
            Ok(data)
        }
        Err(e) => {
            println!("❌ Failed to read audio file: {}", e);
            Err(format!("Failed to read audio file: {}", e))
        }
    }
}

fn main() {
    let audio_state = AudioStateArc::new(Mutex::new(AudioState {
        is_recording: false,
        audio_data: Vec::new(),
    }));

    tauri::Builder::default()
        .plugin(tauri_plugin_mic_recorder::init())
        .manage(audio_state)
        .setup(|app| {
            println!("🚀 MeinWort App setup complete with mic-recorder plugin");
            Ok(())
        })
        .invoke_handler(tauri::generate_handler![
            start_native_recording,
            stop_native_recording,
            check_microphone_access,
            start_window_drag,
            read_audio_file,
            save_recording,
            get_recordings_history,
            delete_recording,
            save_settings,
            load_settings
        ])
        .run(tauri::generate_context!())
        .expect("error while running tauri application");
}