// Prevents additional console window on Windows in release, DO NOT REMOVE!!
#![cfg_attr(not(debug_assertions), windows_subsystem = "windows")]

use tauri::{Manager, Window};
use std::sync::{Arc, Mutex};
use rusqlite::{Connection, Result as SqlResult};
use chrono::{DateTime, Utc};
use serde::{Deserialize, Serialize};
use std::fs;
use std::path::PathBuf;

// Audio state management für Future Verwendung
struct AudioState {
    is_recording: bool,
    audio_data: Vec<f32>,
}

type AudioStateArc = Arc<Mutex<AudioState>>;

// Recording-Historie Datenstrukturen
#[derive(Debug, Serialize, Deserialize, Clone)]
struct Recording {
    id: i64,
    timestamp: DateTime<Utc>,
    duration_seconds: f64,
    transcription: String,
    cost_usd: f64,
    file_path: Option<String>,
}

#[derive(Debug, Serialize, Deserialize)]
struct RecordingInput {
    #[serde(alias = "durationSeconds")]
    duration_seconds: f64,
    transcription: String,
    #[serde(alias = "costUsd")]
    cost_usd: f64,
    #[serde(alias = "filePath")]
    file_path: Option<String>,
}

// App-Einstellungen
#[derive(Debug, Serialize, Deserialize, Clone)]
struct AppSettings {
    openai_api_key: Option<String>,
    auto_clipboard: bool,
    language: String,
}

impl Default for AppSettings {
    fn default() -> Self {
        Self {
            openai_api_key: None,
            auto_clipboard: true,
            language: "de".to_string(),
        }
    }
}

// Kosten-Statistiken
#[derive(Debug, Serialize, Deserialize)]
struct CostStats {
    today_cost: f64,
    week_cost: f64,
    month_cost: f64,
    total_cost: f64,
    today_recordings: i32,
    week_recordings: i32,
    month_recordings: i32,
    total_recordings: i32,
    most_expensive_today: Option<Recording>,
}

// Audio-Device Strukturen
#[derive(Debug, Serialize, Deserialize, Clone)]
struct AudioDevice {
    id: String,
    name: String,
    is_default: bool,
    is_available: bool,
}

#[derive(Debug, Serialize, Deserialize)]
struct AudioDeviceList {
    devices: Vec<AudioDevice>,
    default_device_id: Option<String>,
}

// AI-Processing Strukturen
#[derive(Debug, Serialize, Deserialize, Clone)]
struct TranscriptionEnhancement {
    original_text: String,
    enhanced_text: String,
    summary: Option<String>,
    keywords: Vec<String>,
    action_items: Vec<String>,
    confidence_score: f64,
    processing_time_ms: u64,
}

#[derive(Debug, Serialize, Deserialize)]
struct AIProcessingRequest {
    text: String,
    language: String,
    include_summary: bool,
    include_keywords: bool,
    include_action_items: bool,
}

// Workflow Automation Strukturen
#[derive(Debug, Serialize, Deserialize, Clone)]
struct WorkflowAction {
    id: String,
    name: String,
    action_type: String, // "copy_to_clipboard", "save_to_file", "send_email", "webhook", "notification"
    config: serde_json::Value,
    enabled: bool,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
struct WorkflowTrigger {
    trigger_type: String, // "always", "keyword_detected", "cost_threshold", "duration_threshold"
    condition: Option<serde_json::Value>,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
struct Workflow {
    id: String,
    name: String,
    description: String,
    trigger: WorkflowTrigger,
    actions: Vec<WorkflowAction>,
    enabled: bool,
    created_at: String,
    last_executed: Option<String>,
    execution_count: u32,
}

// Audio Processing Strukturen
#[derive(Debug, Serialize, Deserialize, Clone)]
struct AudioProcessingSettings {
    noise_reduction_enabled: bool,
    noise_reduction_strength: f32, // 0.0 - 1.0
    volume_normalization_enabled: bool,
    target_volume_db: f32,
    voice_enhancement_enabled: bool,
    voice_enhancement_strength: f32, // 0.0 - 1.0
    high_pass_filter_enabled: bool,
    high_pass_cutoff_hz: f32,
}

impl Default for AudioProcessingSettings {
    fn default() -> Self {
        Self {
            noise_reduction_enabled: true,
            noise_reduction_strength: 0.7,
            volume_normalization_enabled: true,
            target_volume_db: -12.0,
            voice_enhancement_enabled: true,
            voice_enhancement_strength: 0.5,
            high_pass_filter_enabled: true,
            high_pass_cutoff_hz: 80.0,
        }
    }
}

#[derive(Debug, Serialize, Deserialize)]
struct AudioQualityAnalysis {
    overall_quality_score: f32, // 0.0 - 1.0
    noise_level: f32, // 0.0 - 1.0
    voice_clarity: f32, // 0.0 - 1.0
    volume_consistency: f32, // 0.0 - 1.0
    frequency_balance: f32, // 0.0 - 1.0
    recommendations: Vec<String>,
    processing_applied: Vec<String>,
}

#[derive(Debug, Serialize, Deserialize)]
struct AudioProcessingResult {
    original_size_bytes: usize,
    processed_size_bytes: usize,
    quality_analysis: AudioQualityAnalysis,
    processing_time_ms: u64,
    improvements_applied: Vec<String>,
}

// Analytics & Insights Strukturen
#[derive(Debug, Serialize, Deserialize)]
struct AnalyticsTimeframe {
    start_date: String,
    end_date: String,
    days: i32,
}

#[derive(Debug, Serialize, Deserialize)]
struct ProductivityMetrics {
    total_recordings: i32,
    total_duration_minutes: f64,
    total_cost_usd: f64,
    average_recording_length: f64,
    recordings_per_day: f64,
    cost_per_minute: f64,
    most_productive_hour: Option<i32>,
    most_productive_day: Option<String>,
    quality_trend: f64, // -1.0 to 1.0 (getting worse to getting better)
}

#[derive(Debug, Serialize, Deserialize)]
struct UsagePattern {
    hour_of_day: i32,
    day_of_week: String,
    recording_count: i32,
    average_duration: f64,
    average_cost: f64,
}

#[derive(Debug, Serialize, Deserialize)]
struct QualityInsight {
    metric_name: String,
    current_value: f64,
    trend: f64, // -1.0 to 1.0
    recommendation: String,
}

#[derive(Debug, Serialize, Deserialize)]
struct AnalyticsReport {
    timeframe: AnalyticsTimeframe,
    productivity_metrics: ProductivityMetrics,
    usage_patterns: Vec<UsagePattern>,
    quality_insights: Vec<QualityInsight>,
    cost_breakdown: CostBreakdown,
    top_keywords: Vec<KeywordFrequency>,
    recommendations: Vec<String>,
}

#[derive(Debug, Serialize, Deserialize)]
struct CostBreakdown {
    by_day: Vec<DailyCost>,
    by_duration: Vec<DurationCost>,
    efficiency_score: f64, // Cost per useful minute
}

#[derive(Debug, Serialize, Deserialize)]
struct DailyCost {
    date: String,
    cost: f64,
    recording_count: i32,
}

#[derive(Debug, Serialize, Deserialize)]
struct DurationCost {
    duration_range: String, // "0-30s", "30s-2m", etc.
    total_cost: f64,
    recording_count: i32,
    average_cost: f64,
}

#[derive(Debug, Serialize, Deserialize)]
struct KeywordFrequency {
    keyword: String,
    frequency: i32,
    trend: f64, // How frequency changed over time
}

#[derive(Debug, Serialize, Deserialize)]
struct WorkflowExecutionResult {
    workflow_id: String,
    success: bool,
    executed_actions: Vec<String>,
    failed_actions: Vec<String>,
    execution_time_ms: u64,
    error_message: Option<String>,
}

#[tauri::command]
async fn start_native_recording(_state: tauri::State<'_, AudioStateArc>) -> Result<String, String> {
    println!("🎤 Native audio recording start requested");
    
    // Plugin-basierte Aufnahme wird über Frontend gesteuert
    Ok("Native recording ready - use plugin commands".to_string())
}

#[tauri::command]
async fn stop_native_recording(_state: tauri::State<'_, AudioStateArc>) -> Result<String, String> {
    println!("🛑 Native audio recording stop requested");
    
    // Plugin-basierte Aufnahme wird über Frontend gesteuert
    Ok("Native recording stopped - use plugin commands".to_string())
}

#[tauri::command]
async fn check_microphone_access() -> Result<String, String> {
    println!("🔍 Checking microphone access...");
    
    // Plugin wird Berechtigung automatisch anfragen
    Ok("Microphone access will be requested by plugin".to_string())
}

#[tauri::command]
async fn start_window_drag(window: Window) -> Result<(), String> {
    window.start_dragging().map_err(|e| e.to_string())
}

// Datenbank-Funktionen
fn init_database() -> SqlResult<Connection> {
    let conn = Connection::open("recordings.db")?;

    conn.execute(
        "CREATE TABLE IF NOT EXISTS recordings (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            timestamp TEXT NOT NULL,
            duration_seconds REAL NOT NULL,
            transcription TEXT NOT NULL,
            cost_usd REAL NOT NULL,
            file_path TEXT
        )",
        [],
    )?;

    println!("✅ Database initialized");
    Ok(conn)
}

#[tauri::command]
async fn save_recording(input: RecordingInput) -> Result<i64, String> {
    println!("💾 Saving recording to database...");
    println!("📊 Duration: {}s, Cost: ${}, Text: {}", input.duration_seconds, input.cost_usd, input.transcription.chars().take(50).collect::<String>());

    let conn = init_database().map_err(|e| format!("Database error: {}", e))?;

    let timestamp = Utc::now();

    let result = conn.execute(
        "INSERT INTO recordings (timestamp, duration_seconds, transcription, cost_usd, file_path)
         VALUES (?1, ?2, ?3, ?4, ?5)",
        [
            &timestamp.to_rfc3339(),
            &input.duration_seconds.to_string(),
            &input.transcription,
            &input.cost_usd.to_string(),
            &input.file_path.unwrap_or_default(),
        ],
    );

    match result {
        Ok(_) => {
            let id = conn.last_insert_rowid();
            println!("✅ Recording saved with ID: {}", id);
            Ok(id)
        }
        Err(e) => {
            println!("❌ Failed to save recording: {}", e);
            Err(format!("Failed to save recording: {}", e))
        }
    }
}

#[tauri::command]
async fn get_recordings_history(limit: Option<i32>) -> Result<Vec<Recording>, String> {
    println!("📋 Getting recordings history...");

    let conn = init_database().map_err(|e| format!("Database error: {}", e))?;
    let limit = limit.unwrap_or(10);

    let mut stmt = conn.prepare(
        "SELECT id, timestamp, duration_seconds, transcription, cost_usd, file_path
         FROM recordings
         ORDER BY timestamp DESC
         LIMIT ?1"
    ).map_err(|e| format!("Prepare error: {}", e))?;

    let recording_iter = stmt.query_map([limit], |row| {
        let timestamp_str: String = row.get(1)?;
        let timestamp = DateTime::parse_from_rfc3339(&timestamp_str)
            .map_err(|_| rusqlite::Error::InvalidColumnType(1, "timestamp".to_string(), rusqlite::types::Type::Text))?
            .with_timezone(&Utc);

        Ok(Recording {
            id: row.get(0)?,
            timestamp,
            duration_seconds: row.get(2)?,
            transcription: row.get(3)?,
            cost_usd: row.get(4)?,
            file_path: row.get(5)?,
        })
    }).map_err(|e| format!("Query error: {}", e))?;

    let mut recordings = Vec::new();
    for recording in recording_iter {
        recordings.push(recording.map_err(|e| format!("Row error: {}", e))?);
    }

    println!("✅ Retrieved {} recordings", recordings.len());
    Ok(recordings)
}

#[tauri::command]
async fn delete_recording(id: i64) -> Result<(), String> {
    println!("🗑️ Deleting recording with ID: {}", id);

    let conn = init_database().map_err(|e| format!("Database error: {}", e))?;

    let result = conn.execute("DELETE FROM recordings WHERE id = ?1", [id]);

    match result {
        Ok(rows_affected) => {
            if rows_affected > 0 {
                println!("✅ Recording deleted successfully");
                Ok(())
            } else {
                Err("Recording not found".to_string())
            }
        }
        Err(e) => {
            println!("❌ Failed to delete recording: {}", e);
            Err(format!("Failed to delete recording: {}", e))
        }
    }
}

// Einstellungen-Funktionen
fn get_settings_path() -> Result<PathBuf, String> {
    let mut path = std::env::current_dir()
        .map_err(|e| format!("Failed to get current directory: {}", e))?;
    path.push("app_data");
    path.push("settings.json");
    Ok(path)
}

#[tauri::command]
async fn save_settings(settings: AppSettings) -> Result<(), String> {
    println!("💾 Saving app settings...");

    let settings_path = get_settings_path()?;

    // Erstelle Verzeichnis falls es nicht existiert
    if let Some(parent) = settings_path.parent() {
        fs::create_dir_all(parent).map_err(|e| format!("Failed to create settings directory: {}", e))?;
    }

    let settings_json = serde_json::to_string_pretty(&settings)
        .map_err(|e| format!("Failed to serialize settings: {}", e))?;

    fs::write(&settings_path, settings_json)
        .map_err(|e| format!("Failed to write settings file: {}", e))?;

    println!("✅ Settings saved to: {:?}", settings_path);
    Ok(())
}

#[tauri::command]
async fn load_settings() -> Result<AppSettings, String> {
    println!("📋 Loading app settings...");

    let settings_path = get_settings_path()?;

    if !settings_path.exists() {
        println!("⚠️ Settings file not found, using defaults");
        return Ok(AppSettings::default());
    }

    let settings_content = fs::read_to_string(&settings_path)
        .map_err(|e| format!("Failed to read settings file: {}", e))?;

    let settings: AppSettings = serde_json::from_str(&settings_content)
        .map_err(|e| format!("Failed to parse settings: {}", e))?;

    println!("✅ Settings loaded from: {:?}", settings_path);
    Ok(settings)
}

#[tauri::command]
async fn get_cost_stats() -> Result<CostStats, String> {
    println!("💰 Getting cost statistics...");

    let conn = init_database().map_err(|e| format!("Database error: {}", e))?;

    let now = Utc::now();
    let today_start = now.date_naive().and_hms_opt(0, 0, 0).unwrap().and_utc();
    let week_start = now - chrono::Duration::days(7);
    let month_start = now - chrono::Duration::days(30);

    // Heute
    let mut stmt = conn.prepare(
        "SELECT SUM(cost_usd), COUNT(*) FROM recordings WHERE timestamp >= ?1"
    ).map_err(|e| format!("Prepare error: {}", e))?;

    let (today_cost, today_recordings): (Option<f64>, i32) = stmt.query_row(
        [today_start.to_rfc3339()],
        |row| Ok((row.get(0)?, row.get(1)?))
    ).map_err(|e| format!("Query error: {}", e))?;

    // Diese Woche
    let mut stmt = conn.prepare(
        "SELECT SUM(cost_usd), COUNT(*) FROM recordings WHERE timestamp >= ?1"
    ).map_err(|e| format!("Prepare error: {}", e))?;

    let (week_cost, week_recordings): (Option<f64>, i32) = stmt.query_row(
        [week_start.to_rfc3339()],
        |row| Ok((row.get(0)?, row.get(1)?))
    ).map_err(|e| format!("Query error: {}", e))?;

    // Dieser Monat
    let mut stmt = conn.prepare(
        "SELECT SUM(cost_usd), COUNT(*) FROM recordings WHERE timestamp >= ?1"
    ).map_err(|e| format!("Prepare error: {}", e))?;

    let (month_cost, month_recordings): (Option<f64>, i32) = stmt.query_row(
        [month_start.to_rfc3339()],
        |row| Ok((row.get(0)?, row.get(1)?))
    ).map_err(|e| format!("Query error: {}", e))?;

    // Gesamt
    let mut stmt = conn.prepare(
        "SELECT SUM(cost_usd), COUNT(*) FROM recordings"
    ).map_err(|e| format!("Prepare error: {}", e))?;

    let (total_cost, total_recordings): (Option<f64>, i32) = stmt.query_row(
        [],
        |row| Ok((row.get(0)?, row.get(1)?))
    ).map_err(|e| format!("Query error: {}", e))?;

    // Teuerste Aufnahme heute
    let mut stmt = conn.prepare(
        "SELECT id, timestamp, duration_seconds, transcription, cost_usd, file_path
         FROM recordings
         WHERE timestamp >= ?1
         ORDER BY cost_usd DESC
         LIMIT 1"
    ).map_err(|e| format!("Prepare error: {}", e))?;

    let most_expensive_today = stmt.query_row(
        [today_start.to_rfc3339()],
        |row| {
            let timestamp_str: String = row.get(1)?;
            let timestamp = DateTime::parse_from_rfc3339(&timestamp_str)
                .map_err(|_| rusqlite::Error::InvalidColumnType(1, "timestamp".to_string(), rusqlite::types::Type::Text))?
                .with_timezone(&Utc);

            Ok(Recording {
                id: row.get(0)?,
                timestamp,
                duration_seconds: row.get(2)?,
                transcription: row.get(3)?,
                cost_usd: row.get(4)?,
                file_path: row.get(5)?,
            })
        }
    ).ok();

    let stats = CostStats {
        today_cost: today_cost.unwrap_or(0.0),
        week_cost: week_cost.unwrap_or(0.0),
        month_cost: month_cost.unwrap_or(0.0),
        total_cost: total_cost.unwrap_or(0.0),
        today_recordings,
        week_recordings,
        month_recordings,
        total_recordings,
        most_expensive_today,
    };

    println!("✅ Cost stats calculated: Today ${:.4}, Week ${:.4}, Month ${:.4}",
             stats.today_cost, stats.week_cost, stats.month_cost);

    Ok(stats)
}

#[tauri::command]
async fn get_audio_devices() -> Result<AudioDeviceList, String> {
    println!("🎤 Getting available audio devices...");

    // Für jetzt simulieren wir Audio-Geräte
    // In einer echten Implementierung würde man hier das Audio-System abfragen
    let devices = vec![
        AudioDevice {
            id: "default".to_string(),
            name: "Standard-Mikrofon".to_string(),
            is_default: true,
            is_available: true,
        },
        AudioDevice {
            id: "builtin".to_string(),
            name: "Eingebautes Mikrofon".to_string(),
            is_default: false,
            is_available: true,
        },
        AudioDevice {
            id: "usb_mic".to_string(),
            name: "USB-Mikrofon".to_string(),
            is_default: false,
            is_available: false, // Nicht angeschlossen
        },
    ];

    let device_list = AudioDeviceList {
        default_device_id: Some("default".to_string()),
        devices,
    };

    println!("✅ Found {} audio devices", device_list.devices.len());
    Ok(device_list)
}

#[tauri::command]
async fn test_audio_device(device_id: String) -> Result<bool, String> {
    println!("🧪 Testing audio device: {}", device_id);

    // Simuliere Audio-Device Test
    // In einer echten Implementierung würde man hier das Mikrofon testen
    tokio::time::sleep(tokio::time::Duration::from_millis(1000)).await;

    // Simuliere Erfolg für verfügbare Geräte
    let success = !device_id.contains("usb") || device_id == "default";

    if success {
        println!("✅ Audio device test successful: {}", device_id);
    } else {
        println!("❌ Audio device test failed: {}", device_id);
    }

    Ok(success)
}

#[tauri::command]
async fn set_preferred_audio_device(device_id: String) -> Result<(), String> {
    println!("🎤 Setting preferred audio device: {}", device_id);

    // Hier würde man das bevorzugte Audio-Gerät im System setzen
    // Für jetzt loggen wir es nur

    println!("✅ Preferred audio device set to: {}", device_id);
    Ok(())
}

#[tauri::command]
async fn enhance_transcription(request: AIProcessingRequest) -> Result<TranscriptionEnhancement, String> {
    println!("🤖 Enhancing transcription with AI...");
    let start_time = std::time::Instant::now();

    let text = request.text.trim();
    if text.is_empty() {
        return Err("Empty text provided".to_string());
    }

    // Simuliere AI-Processing (in echter App würde hier OpenAI GPT-4 verwendet)
    tokio::time::sleep(tokio::time::Duration::from_millis(500)).await;

    // Text-Enhancement: Entferne Füllwörter und verbessere Interpunktion
    let enhanced_text = enhance_text_quality(text);

    // Generiere Zusammenfassung
    let summary = if request.include_summary {
        Some(generate_summary(&enhanced_text))
    } else {
        None
    };

    // Extrahiere Schlüsselwörter
    let keywords = if request.include_keywords {
        extract_keywords(&enhanced_text)
    } else {
        Vec::new()
    };

    // Erkenne Action Items
    let action_items = if request.include_action_items {
        extract_action_items(&enhanced_text)
    } else {
        Vec::new()
    };

    let processing_time = start_time.elapsed().as_millis() as u64;

    let enhancement = TranscriptionEnhancement {
        original_text: text.to_string(),
        enhanced_text,
        summary,
        keywords,
        action_items,
        confidence_score: 0.92, // Simuliert
        processing_time_ms: processing_time,
    };

    println!("✅ Transcription enhanced in {}ms", processing_time);
    Ok(enhancement)
}

// Hilfsfunktionen für AI-Processing
fn enhance_text_quality(text: &str) -> String {
    let mut enhanced = text.to_string();

    // Entferne häufige Füllwörter
    let filler_words = ["äh", "ähm", "also", "halt", "irgendwie", "sozusagen"];
    for word in filler_words {
        enhanced = enhanced.replace(&format!(" {} ", word), " ");
        enhanced = enhanced.replace(&format!("{} ", word), "");
    }

    // Verbessere Interpunktion
    enhanced = enhanced.replace(" .", ".");
    enhanced = enhanced.replace(" ,", ",");
    enhanced = enhanced.replace(" ?", "?");
    enhanced = enhanced.replace(" !", "!");

    // Erste Buchstabe groß
    if let Some(first_char) = enhanced.chars().next() {
        enhanced = first_char.to_uppercase().collect::<String>() + &enhanced[1..];
    }

    // Sätze mit Punkt beenden wenn nötig
    if !enhanced.ends_with('.') && !enhanced.ends_with('!') && !enhanced.ends_with('?') {
        enhanced.push('.');
    }

    enhanced
}

fn generate_summary(text: &str) -> String {
    if text.len() < 50 {
        return text.to_string();
    }

    // Einfache Zusammenfassung: Erste 100 Zeichen + wichtigste Wörter
    let words: Vec<&str> = text.split_whitespace().collect();
    let important_words: Vec<&str> = words.iter()
        .filter(|word| word.len() > 4 && !["diese", "haben", "werden", "können", "sollen"].contains(word))
        .take(5)
        .copied()
        .collect();

    if words.len() > 15 {
        format!("Zusammenfassung: {}... Schlüsselthemen: {}",
                words[..10].join(" "),
                important_words.join(", "))
    } else {
        text.to_string()
    }
}

fn extract_keywords(text: &str) -> Vec<String> {
    let words: Vec<&str> = text.split_whitespace().collect();
    let mut keywords = Vec::new();

    // Finde wichtige Wörter (länger als 4 Zeichen, keine Füllwörter)
    for word in words {
        let clean_word = word.trim_matches(|c: char| !c.is_alphabetic()).to_lowercase();
        if clean_word.len() > 4
            && !["diese", "haben", "werden", "können", "sollen", "möchte", "würde"].contains(&clean_word.as_str())
            && !keywords.contains(&clean_word) {
            keywords.push(clean_word);
        }
    }

    keywords.truncate(8); // Maximal 8 Keywords
    keywords
}

fn extract_action_items(text: &str) -> Vec<String> {
    let mut action_items = Vec::new();
    let sentences: Vec<&str> = text.split(&['.', '!', '?'][..]).collect();

    // Suche nach Action-Indikatoren
    let action_indicators = ["muss", "soll", "wird", "todo", "aufgabe", "erledigen", "machen", "bis"];

    for sentence in sentences {
        let sentence = sentence.trim();
        if sentence.len() > 10 {
            for indicator in action_indicators {
                if sentence.to_lowercase().contains(indicator) {
                    action_items.push(format!("• {}", sentence));
                    break;
                }
            }
        }
    }

    action_items.truncate(5); // Maximal 5 Action Items
    action_items
}

#[tauri::command]
async fn execute_workflow(workflow: Workflow, transcription_data: serde_json::Value) -> Result<WorkflowExecutionResult, String> {
    println!("⚡ Executing workflow: {}", workflow.name);
    let start_time = std::time::Instant::now();

    let mut executed_actions = Vec::new();
    let mut failed_actions = Vec::new();

    for action in &workflow.actions {
        if !action.enabled {
            continue;
        }

        println!("🔄 Executing action: {} ({})", action.name, action.action_type);

        match execute_workflow_action(action, &transcription_data).await {
            Ok(_) => {
                executed_actions.push(action.name.clone());
                println!("✅ Action completed: {}", action.name);
            }
            Err(e) => {
                failed_actions.push(action.name.clone());
                println!("❌ Action failed: {} - {}", action.name, e);
            }
        }
    }

    let execution_time = start_time.elapsed().as_millis() as u64;
    let success = failed_actions.is_empty();

    let result = WorkflowExecutionResult {
        workflow_id: workflow.id.clone(),
        success,
        executed_actions,
        failed_actions,
        execution_time_ms: execution_time,
        error_message: if success { None } else { Some("Some actions failed".to_string()) },
    };

    println!("⚡ Workflow execution completed in {}ms: {} actions succeeded, {} failed",
             execution_time, result.executed_actions.len(), result.failed_actions.len());

    Ok(result)
}

async fn execute_workflow_action(action: &WorkflowAction, data: &serde_json::Value) -> Result<(), String> {
    match action.action_type.as_str() {
        "copy_to_clipboard" => {
            // Simuliere Clipboard-Aktion
            println!("📋 Copying to clipboard: {}", data.get("text").unwrap_or(&serde_json::Value::Null));
            Ok(())
        }
        "save_to_file" => {
            // Simuliere Datei-Speicherung
            let file_path = action.config.get("file_path").and_then(|v| v.as_str()).unwrap_or("output.txt");
            println!("💾 Saving to file: {}", file_path);

            // In echter Implementierung würde hier die Datei geschrieben
            tokio::time::sleep(tokio::time::Duration::from_millis(100)).await;
            Ok(())
        }
        "send_notification" => {
            // Simuliere Benachrichtigung
            let message = action.config.get("message").and_then(|v| v.as_str()).unwrap_or("Workflow completed");
            println!("🔔 Sending notification: {}", message);
            Ok(())
        }
        "webhook" => {
            // Simuliere Webhook-Call
            let url = action.config.get("url").and_then(|v| v.as_str()).unwrap_or("http://localhost");
            println!("🌐 Calling webhook: {}", url);

            // Simuliere HTTP-Request
            tokio::time::sleep(tokio::time::Duration::from_millis(200)).await;
            Ok(())
        }
        "email" => {
            // Simuliere E-Mail versenden
            let to = action.config.get("to").and_then(|v| v.as_str()).unwrap_or("<EMAIL>");
            println!("📧 Sending email to: {}", to);

            tokio::time::sleep(tokio::time::Duration::from_millis(300)).await;
            Ok(())
        }
        _ => {
            Err(format!("Unknown action type: {}", action.action_type))
        }
    }
}

#[tauri::command]
async fn get_workflow_templates() -> Result<Vec<Workflow>, String> {
    println!("📋 Getting workflow templates...");

    let templates = vec![
        Workflow {
            id: "template_basic_copy".to_string(),
            name: "Einfach kopieren".to_string(),
            description: "Kopiert die Transkription automatisch in die Zwischenablage".to_string(),
            trigger: WorkflowTrigger {
                trigger_type: "always".to_string(),
                condition: None,
            },
            actions: vec![
                WorkflowAction {
                    id: "copy_action".to_string(),
                    name: "In Zwischenablage kopieren".to_string(),
                    action_type: "copy_to_clipboard".to_string(),
                    config: serde_json::json!({}),
                    enabled: true,
                }
            ],
            enabled: true,
            created_at: chrono::Utc::now().to_rfc3339(),
            last_executed: None,
            execution_count: 0,
        },
        Workflow {
            id: "template_save_and_notify".to_string(),
            name: "Speichern & Benachrichtigen".to_string(),
            description: "Speichert die Transkription in einer Datei und sendet eine Benachrichtigung".to_string(),
            trigger: WorkflowTrigger {
                trigger_type: "always".to_string(),
                condition: None,
            },
            actions: vec![
                WorkflowAction {
                    id: "save_action".to_string(),
                    name: "In Datei speichern".to_string(),
                    action_type: "save_to_file".to_string(),
                    config: serde_json::json!({
                        "file_path": "transcriptions/latest.txt",
                        "append": true
                    }),
                    enabled: true,
                },
                WorkflowAction {
                    id: "notify_action".to_string(),
                    name: "Benachrichtigung senden".to_string(),
                    action_type: "send_notification".to_string(),
                    config: serde_json::json!({
                        "message": "Neue Transkription gespeichert!"
                    }),
                    enabled: true,
                }
            ],
            enabled: true,
            created_at: chrono::Utc::now().to_rfc3339(),
            last_executed: None,
            execution_count: 0,
        },
        Workflow {
            id: "template_meeting_workflow".to_string(),
            name: "Meeting-Workflow".to_string(),
            description: "Für wichtige Meetings: Speichern, E-Mail senden und Webhook aufrufen".to_string(),
            trigger: WorkflowTrigger {
                trigger_type: "keyword_detected".to_string(),
                condition: Some(serde_json::json!({
                    "keywords": ["meeting", "besprechung", "termin"]
                })),
            },
            actions: vec![
                WorkflowAction {
                    id: "save_meeting".to_string(),
                    name: "Meeting-Protokoll speichern".to_string(),
                    action_type: "save_to_file".to_string(),
                    config: serde_json::json!({
                        "file_path": "meetings/meeting_{timestamp}.txt"
                    }),
                    enabled: true,
                },
                WorkflowAction {
                    id: "email_team".to_string(),
                    name: "Team benachrichtigen".to_string(),
                    action_type: "email".to_string(),
                    config: serde_json::json!({
                        "to": "<EMAIL>",
                        "subject": "Neues Meeting-Protokoll"
                    }),
                    enabled: true,
                },
                WorkflowAction {
                    id: "webhook_crm".to_string(),
                    name: "CRM aktualisieren".to_string(),
                    action_type: "webhook".to_string(),
                    config: serde_json::json!({
                        "url": "https://api.company.com/meetings",
                        "method": "POST"
                    }),
                    enabled: true,
                }
            ],
            enabled: false, // Standardmäßig deaktiviert
            created_at: chrono::Utc::now().to_rfc3339(),
            last_executed: None,
            execution_count: 0,
        }
    ];

    println!("✅ Retrieved {} workflow templates", templates.len());
    Ok(templates)
}

#[tauri::command]
async fn process_audio(audio_data: Vec<u8>, settings: AudioProcessingSettings) -> Result<AudioProcessingResult, String> {
    println!("🎵 Processing audio with {} bytes...", audio_data.len());
    let start_time = std::time::Instant::now();

    if audio_data.is_empty() {
        return Err("No audio data provided".to_string());
    }

    let mut improvements_applied = Vec::new();
    let mut processing_applied = Vec::new();

    // Simuliere Audio-Processing (in echter App würde hier echte DSP verwendet)
    tokio::time::sleep(tokio::time::Duration::from_millis(300)).await;

    // Noise Reduction
    if settings.noise_reduction_enabled {
        improvements_applied.push(format!("Noise Reduction ({}%)", (settings.noise_reduction_strength * 100.0) as u32));
        processing_applied.push("noise_reduction".to_string());
        println!("🔇 Applied noise reduction: {}%", (settings.noise_reduction_strength * 100.0) as u32);
    }

    // Volume Normalization
    if settings.volume_normalization_enabled {
        improvements_applied.push(format!("Volume Normalization ({}dB)", settings.target_volume_db));
        processing_applied.push("volume_normalization".to_string());
        println!("🔊 Applied volume normalization: {}dB", settings.target_volume_db);
    }

    // Voice Enhancement
    if settings.voice_enhancement_enabled {
        improvements_applied.push(format!("Voice Enhancement ({}%)", (settings.voice_enhancement_strength * 100.0) as u32));
        processing_applied.push("voice_enhancement".to_string());
        println!("🎤 Applied voice enhancement: {}%", (settings.voice_enhancement_strength * 100.0) as u32);
    }

    // High-pass Filter
    if settings.high_pass_filter_enabled {
        improvements_applied.push(format!("High-pass Filter ({}Hz)", settings.high_pass_cutoff_hz));
        processing_applied.push("high_pass_filter".to_string());
        println!("🎛️ Applied high-pass filter: {}Hz", settings.high_pass_cutoff_hz);
    }

    // Audio Quality Analysis
    let quality_analysis = analyze_audio_quality(&audio_data, &processing_applied);

    let processing_time = start_time.elapsed().as_millis() as u64;

    // Simuliere verarbeitete Audio-Größe (normalerweise etwas kleiner durch Kompression)
    let processed_size = (audio_data.len() as f32 * 0.95) as usize;

    let result = AudioProcessingResult {
        original_size_bytes: audio_data.len(),
        processed_size_bytes: processed_size,
        quality_analysis,
        processing_time_ms: processing_time,
        improvements_applied,
    };

    println!("✅ Audio processing completed in {}ms", processing_time);
    Ok(result)
}

fn analyze_audio_quality(audio_data: &[u8], processing_applied: &[String]) -> AudioQualityAnalysis {
    // Simuliere Audio-Qualitätsanalyse
    let base_quality = 0.6; // Basis-Qualität

    // Qualität verbessert sich durch Processing
    let mut quality_boost = 0.0;
    let mut recommendations = Vec::new();

    if processing_applied.contains(&"noise_reduction".to_string()) {
        quality_boost += 0.15;
    } else {
        recommendations.push("Noise Reduction aktivieren für bessere Qualität".to_string());
    }

    if processing_applied.contains(&"volume_normalization".to_string()) {
        quality_boost += 0.1;
    } else {
        recommendations.push("Volume Normalization für konsistente Lautstärke".to_string());
    }

    if processing_applied.contains(&"voice_enhancement".to_string()) {
        quality_boost += 0.1;
    } else {
        recommendations.push("Voice Enhancement für klarere Stimme".to_string());
    }

    // Simuliere verschiedene Qualitätsmetriken
    let overall_quality = (base_quality + quality_boost).min(1.0);
    let noise_level = if processing_applied.contains(&"noise_reduction".to_string()) { 0.2 } else { 0.7 };
    let voice_clarity = if processing_applied.contains(&"voice_enhancement".to_string()) { 0.85 } else { 0.6 };
    let volume_consistency = if processing_applied.contains(&"volume_normalization".to_string()) { 0.9 } else { 0.5 };
    let frequency_balance = if processing_applied.contains(&"high_pass_filter".to_string()) { 0.8 } else { 0.6 };

    // Zusätzliche Empfehlungen basierend auf Dateigröße
    if audio_data.len() < 50000 {
        recommendations.push("Aufnahme ist sehr kurz - längere Aufnahmen haben bessere Qualität".to_string());
    }

    if overall_quality > 0.8 {
        recommendations.push("Ausgezeichnete Audio-Qualität erreicht!".to_string());
    }

    AudioQualityAnalysis {
        overall_quality_score: overall_quality,
        noise_level,
        voice_clarity,
        volume_consistency,
        frequency_balance,
        recommendations,
        processing_applied: processing_applied.to_vec(),
    }
}

#[tauri::command]
async fn get_audio_processing_presets() -> Result<Vec<AudioProcessingSettings>, String> {
    println!("🎛️ Getting audio processing presets...");

    let presets = vec![
        AudioProcessingSettings {
            noise_reduction_enabled: false,
            noise_reduction_strength: 0.0,
            volume_normalization_enabled: false,
            target_volume_db: 0.0,
            voice_enhancement_enabled: false,
            voice_enhancement_strength: 0.0,
            high_pass_filter_enabled: false,
            high_pass_cutoff_hz: 0.0,
        }, // "Aus" Preset
        AudioProcessingSettings {
            noise_reduction_enabled: true,
            noise_reduction_strength: 0.3,
            volume_normalization_enabled: true,
            target_volume_db: -15.0,
            voice_enhancement_enabled: false,
            voice_enhancement_strength: 0.0,
            high_pass_filter_enabled: true,
            high_pass_cutoff_hz: 60.0,
        }, // "Leicht" Preset
        AudioProcessingSettings::default(), // "Standard" Preset
        AudioProcessingSettings {
            noise_reduction_enabled: true,
            noise_reduction_strength: 0.9,
            volume_normalization_enabled: true,
            target_volume_db: -10.0,
            voice_enhancement_enabled: true,
            voice_enhancement_strength: 0.8,
            high_pass_filter_enabled: true,
            high_pass_cutoff_hz: 100.0,
        }, // "Aggressiv" Preset
    ];

    println!("✅ Retrieved {} audio processing presets", presets.len());
    Ok(presets)
}

#[tauri::command]
async fn get_analytics_report(days: i32) -> Result<AnalyticsReport, String> {
    println!("📊 Generating analytics report for {} days...", days);

    let conn = init_database().map_err(|e| format!("Database error: {}", e))?;

    let end_date = Utc::now();
    let start_date = end_date - chrono::Duration::days(days as i64);

    let timeframe = AnalyticsTimeframe {
        start_date: start_date.format("%Y-%m-%d").to_string(),
        end_date: end_date.format("%Y-%m-%d").to_string(),
        days,
    };

    // Basis-Metriken aus der Datenbank
    let mut stmt = conn.prepare(
        "SELECT COUNT(*), SUM(duration_seconds), SUM(cost_usd), AVG(duration_seconds)
         FROM recordings
         WHERE timestamp >= ?1"
    ).map_err(|e| format!("Prepare error: {}", e))?;

    let (total_recordings, total_duration_seconds, total_cost, avg_duration): (i32, Option<f64>, Option<f64>, Option<f64>) = stmt.query_row(
        [start_date.to_rfc3339()],
        |row| Ok((row.get(0)?, row.get(1)?, row.get(2)?, row.get(3)?))
    ).unwrap_or((0, None, None, None));

    let total_duration_minutes = total_duration_seconds.unwrap_or(0.0) / 60.0;
    let total_cost_usd = total_cost.unwrap_or(0.0);
    let average_recording_length = avg_duration.unwrap_or(0.0);

    // Produktivitäts-Metriken berechnen
    let recordings_per_day = if days > 0 { total_recordings as f64 / days as f64 } else { 0.0 };
    let cost_per_minute = if total_duration_minutes > 0.0 { total_cost_usd / total_duration_minutes } else { 0.0 };

    // Simuliere erweiterte Metriken (in echter App aus DB)
    let productivity_metrics = ProductivityMetrics {
        total_recordings,
        total_duration_minutes,
        total_cost_usd,
        average_recording_length,
        recordings_per_day,
        cost_per_minute,
        most_productive_hour: Some(14), // 14:00 Uhr
        most_productive_day: Some("Dienstag".to_string()),
        quality_trend: 0.15, // Verbesserung um 15%
    };

    // Usage Patterns simulieren
    let usage_patterns = generate_usage_patterns();

    // Quality Insights
    let quality_insights = vec![
        QualityInsight {
            metric_name: "Aufnahme-Qualität".to_string(),
            current_value: 0.82,
            trend: 0.12,
            recommendation: "Ihre Audio-Qualität hat sich um 12% verbessert! Weiter so.".to_string(),
        },
        QualityInsight {
            metric_name: "Transkriptions-Genauigkeit".to_string(),
            current_value: 0.94,
            trend: 0.05,
            recommendation: "Sehr gute Transkriptions-Genauigkeit. Versuchen Sie ruhigere Umgebungen für noch bessere Ergebnisse.".to_string(),
        },
        QualityInsight {
            metric_name: "Kosten-Effizienz".to_string(),
            current_value: 0.78,
            trend: -0.03,
            recommendation: "Kosten pro Minute leicht gestiegen. Kürzere, fokussiertere Aufnahmen können helfen.".to_string(),
        },
    ];

    // Cost Breakdown
    let cost_breakdown = generate_cost_breakdown(&conn, &start_date)?;

    // Top Keywords (simuliert)
    let top_keywords = vec![
        KeywordFrequency { keyword: "projekt".to_string(), frequency: 15, trend: 0.2 },
        KeywordFrequency { keyword: "meeting".to_string(), frequency: 12, trend: 0.1 },
        KeywordFrequency { keyword: "deadline".to_string(), frequency: 8, trend: -0.1 },
        KeywordFrequency { keyword: "team".to_string(), frequency: 7, trend: 0.3 },
        KeywordFrequency { keyword: "aufgabe".to_string(), frequency: 6, trend: 0.0 },
    ];

    // Intelligente Empfehlungen
    let recommendations = generate_recommendations(&productivity_metrics, &quality_insights);

    let report = AnalyticsReport {
        timeframe,
        productivity_metrics,
        usage_patterns,
        quality_insights,
        cost_breakdown,
        top_keywords,
        recommendations,
    };

    println!("✅ Analytics report generated successfully");
    Ok(report)
}

fn generate_usage_patterns() -> Vec<UsagePattern> {
    vec![
        UsagePattern {
            hour_of_day: 9,
            day_of_week: "Montag".to_string(),
            recording_count: 5,
            average_duration: 45.2,
            average_cost: 0.003,
        },
        UsagePattern {
            hour_of_day: 14,
            day_of_week: "Dienstag".to_string(),
            recording_count: 8,
            average_duration: 62.1,
            average_cost: 0.004,
        },
        UsagePattern {
            hour_of_day: 11,
            day_of_week: "Mittwoch".to_string(),
            recording_count: 6,
            average_duration: 38.7,
            average_cost: 0.002,
        },
    ]
}

fn generate_cost_breakdown(conn: &Connection, start_date: &DateTime<Utc>) -> Result<CostBreakdown, String> {
    // Tägliche Kosten (simuliert)
    let by_day = vec![
        DailyCost { date: "2024-01-15".to_string(), cost: 0.12, recording_count: 4 },
        DailyCost { date: "2024-01-16".to_string(), cost: 0.08, recording_count: 3 },
        DailyCost { date: "2024-01-17".to_string(), cost: 0.15, recording_count: 6 },
    ];

    // Kosten nach Dauer
    let by_duration = vec![
        DurationCost {
            duration_range: "0-30s".to_string(),
            total_cost: 0.05,
            recording_count: 8,
            average_cost: 0.006,
        },
        DurationCost {
            duration_range: "30s-2m".to_string(),
            total_cost: 0.18,
            recording_count: 12,
            average_cost: 0.015,
        },
        DurationCost {
            duration_range: "2m+".to_string(),
            total_cost: 0.32,
            recording_count: 5,
            average_cost: 0.064,
        },
    ];

    // Effizienz-Score (Kosten pro nützlicher Minute)
    let efficiency_score = 0.78;

    Ok(CostBreakdown {
        by_day,
        by_duration,
        efficiency_score,
    })
}

fn generate_recommendations(metrics: &ProductivityMetrics, insights: &[QualityInsight]) -> Vec<String> {
    let mut recommendations = Vec::new();

    // Produktivitäts-Empfehlungen
    if metrics.recordings_per_day < 2.0 {
        recommendations.push("💡 Versuchen Sie, täglich mindestens 2-3 Aufnahmen zu machen für bessere Produktivität.".to_string());
    }

    if metrics.average_recording_length > 120.0 {
        recommendations.push("⏱️ Kürzere Aufnahmen (unter 2 Minuten) sind oft effizienter und kostengünstiger.".to_string());
    }

    if metrics.cost_per_minute > 0.05 {
        recommendations.push("💰 Ihre Kosten pro Minute sind hoch. Versuchen Sie klarere Sprache für bessere Transkription.".to_string());
    }

    // Qualitäts-Empfehlungen
    for insight in insights {
        if insight.trend < -0.05 {
            recommendations.push(format!("📉 {}: {}", insight.metric_name, insight.recommendation));
        } else if insight.trend > 0.1 {
            recommendations.push(format!("📈 {}: Großartige Verbesserung!", insight.metric_name));
        }
    }

    // Timing-Empfehlungen
    if let Some(hour) = metrics.most_productive_hour {
        recommendations.push(format!("🕐 Ihre produktivste Zeit ist um {}:00 Uhr. Planen Sie wichtige Aufnahmen zu dieser Zeit.", hour));
    }

    // Allgemeine Tipps
    recommendations.push("🎯 Tipp: Verwenden Sie Schlüsselwörter am Anfang für bessere Organisation.".to_string());
    recommendations.push("🔧 Nutzen Sie Audio Processing für bessere Qualität bei schwierigen Bedingungen.".to_string());

    recommendations.truncate(8); // Maximal 8 Empfehlungen
    recommendations
}

#[tauri::command]
async fn read_audio_file(file_path: String) -> Result<Vec<u8>, String> {
    println!("📁 Reading audio file: {}", file_path);

    // Prüfe ob die Datei existiert
    if !std::path::Path::new(&file_path).exists() {
        return Err(format!("Audio file not found: {}", file_path));
    }

    match std::fs::read(&file_path) {
        Ok(data) => {
            println!("✅ Audio file read successfully, size: {} bytes", data.len());
            Ok(data)
        }
        Err(e) => {
            println!("❌ Failed to read audio file: {}", e);
            Err(format!("Failed to read audio file: {}", e))
        }
    }
}

fn main() {
    let audio_state = AudioStateArc::new(Mutex::new(AudioState {
        is_recording: false,
        audio_data: Vec::new(),
    }));

    tauri::Builder::default()
        .plugin(tauri_plugin_mic_recorder::init())
        .manage(audio_state)
        .setup(|app| {
            println!("🚀 MeinWort App setup complete with mic-recorder plugin");
            Ok(())
        })
        .invoke_handler(tauri::generate_handler![
            start_native_recording,
            stop_native_recording,
            check_microphone_access,
            start_window_drag,
            read_audio_file,
            save_recording,
            get_recordings_history,
            delete_recording,
            save_settings,
            load_settings,
            get_cost_stats,
            get_audio_devices,
            test_audio_device,
            set_preferred_audio_device,
            enhance_transcription,
            execute_workflow,
            get_workflow_templates,
            process_audio,
            get_audio_processing_presets,
            get_analytics_report
        ])
        .run(tauri::generate_context!())
        .expect("error while running tauri application");
}