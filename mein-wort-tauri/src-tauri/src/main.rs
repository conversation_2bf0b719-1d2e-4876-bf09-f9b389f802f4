// Prevents additional console window on Windows in release, DO NOT REMOVE!!
#![cfg_attr(not(debug_assertions), windows_subsystem = "windows")]

use tauri::{Manager, Window};
use std::sync::{Arc, Mutex};
use rusqlite::{Connection, Result as SqlResult};
use chrono::{DateTime, Utc};
use serde::{Deserialize, Serialize};
use std::fs;
use std::path::PathBuf;

// Audio state management für Future Verwendung
struct AudioState {
    is_recording: bool,
    audio_data: Vec<f32>,
}

type AudioStateArc = Arc<Mutex<AudioState>>;

// Recording-Historie Datenstrukturen
#[derive(Debug, Serialize, Deserialize, Clone)]
struct Recording {
    id: i64,
    timestamp: DateTime<Utc>,
    duration_seconds: f64,
    transcription: String,
    cost_usd: f64,
    file_path: Option<String>,
}

#[derive(Debug, Serialize, Deserialize)]
struct RecordingInput {
    #[serde(alias = "durationSeconds")]
    duration_seconds: f64,
    transcription: String,
    #[serde(alias = "costUsd")]
    cost_usd: f64,
    #[serde(alias = "filePath")]
    file_path: Option<String>,
}

// App-Einstellungen
#[derive(Debug, Serialize, Deserialize, Clone)]
struct AppSettings {
    openai_api_key: Option<String>,
    auto_clipboard: bool,
    language: String,
}

impl Default for AppSettings {
    fn default() -> Self {
        Self {
            openai_api_key: None,
            auto_clipboard: true,
            language: "de".to_string(),
        }
    }
}

// Kosten-Statistiken
#[derive(Debug, Serialize, Deserialize)]
struct CostStats {
    today_cost: f64,
    week_cost: f64,
    month_cost: f64,
    total_cost: f64,
    today_recordings: i32,
    week_recordings: i32,
    month_recordings: i32,
    total_recordings: i32,
    most_expensive_today: Option<Recording>,
}

// Audio-Device Strukturen
#[derive(Debug, Serialize, Deserialize, Clone)]
struct AudioDevice {
    id: String,
    name: String,
    is_default: bool,
    is_available: bool,
}

#[derive(Debug, Serialize, Deserialize)]
struct AudioDeviceList {
    devices: Vec<AudioDevice>,
    default_device_id: Option<String>,
}

// AI-Processing Strukturen
#[derive(Debug, Serialize, Deserialize, Clone)]
struct TranscriptionEnhancement {
    original_text: String,
    enhanced_text: String,
    summary: Option<String>,
    keywords: Vec<String>,
    action_items: Vec<String>,
    confidence_score: f64,
    processing_time_ms: u64,
}

#[derive(Debug, Serialize, Deserialize)]
struct AIProcessingRequest {
    text: String,
    language: String,
    include_summary: bool,
    include_keywords: bool,
    include_action_items: bool,
}

#[tauri::command]
async fn start_native_recording(_state: tauri::State<'_, AudioStateArc>) -> Result<String, String> {
    println!("🎤 Native audio recording start requested");
    
    // Plugin-basierte Aufnahme wird über Frontend gesteuert
    Ok("Native recording ready - use plugin commands".to_string())
}

#[tauri::command]
async fn stop_native_recording(_state: tauri::State<'_, AudioStateArc>) -> Result<String, String> {
    println!("🛑 Native audio recording stop requested");
    
    // Plugin-basierte Aufnahme wird über Frontend gesteuert
    Ok("Native recording stopped - use plugin commands".to_string())
}

#[tauri::command]
async fn check_microphone_access() -> Result<String, String> {
    println!("🔍 Checking microphone access...");
    
    // Plugin wird Berechtigung automatisch anfragen
    Ok("Microphone access will be requested by plugin".to_string())
}

#[tauri::command]
async fn start_window_drag(window: Window) -> Result<(), String> {
    window.start_dragging().map_err(|e| e.to_string())
}

// Datenbank-Funktionen
fn init_database() -> SqlResult<Connection> {
    let conn = Connection::open("recordings.db")?;

    conn.execute(
        "CREATE TABLE IF NOT EXISTS recordings (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            timestamp TEXT NOT NULL,
            duration_seconds REAL NOT NULL,
            transcription TEXT NOT NULL,
            cost_usd REAL NOT NULL,
            file_path TEXT
        )",
        [],
    )?;

    println!("✅ Database initialized");
    Ok(conn)
}

#[tauri::command]
async fn save_recording(input: RecordingInput) -> Result<i64, String> {
    println!("💾 Saving recording to database...");
    println!("📊 Duration: {}s, Cost: ${}, Text: {}", input.duration_seconds, input.cost_usd, input.transcription.chars().take(50).collect::<String>());

    let conn = init_database().map_err(|e| format!("Database error: {}", e))?;

    let timestamp = Utc::now();

    let result = conn.execute(
        "INSERT INTO recordings (timestamp, duration_seconds, transcription, cost_usd, file_path)
         VALUES (?1, ?2, ?3, ?4, ?5)",
        [
            &timestamp.to_rfc3339(),
            &input.duration_seconds.to_string(),
            &input.transcription,
            &input.cost_usd.to_string(),
            &input.file_path.unwrap_or_default(),
        ],
    );

    match result {
        Ok(_) => {
            let id = conn.last_insert_rowid();
            println!("✅ Recording saved with ID: {}", id);
            Ok(id)
        }
        Err(e) => {
            println!("❌ Failed to save recording: {}", e);
            Err(format!("Failed to save recording: {}", e))
        }
    }
}

#[tauri::command]
async fn get_recordings_history(limit: Option<i32>) -> Result<Vec<Recording>, String> {
    println!("📋 Getting recordings history...");

    let conn = init_database().map_err(|e| format!("Database error: {}", e))?;
    let limit = limit.unwrap_or(10);

    let mut stmt = conn.prepare(
        "SELECT id, timestamp, duration_seconds, transcription, cost_usd, file_path
         FROM recordings
         ORDER BY timestamp DESC
         LIMIT ?1"
    ).map_err(|e| format!("Prepare error: {}", e))?;

    let recording_iter = stmt.query_map([limit], |row| {
        let timestamp_str: String = row.get(1)?;
        let timestamp = DateTime::parse_from_rfc3339(&timestamp_str)
            .map_err(|_| rusqlite::Error::InvalidColumnType(1, "timestamp".to_string(), rusqlite::types::Type::Text))?
            .with_timezone(&Utc);

        Ok(Recording {
            id: row.get(0)?,
            timestamp,
            duration_seconds: row.get(2)?,
            transcription: row.get(3)?,
            cost_usd: row.get(4)?,
            file_path: row.get(5)?,
        })
    }).map_err(|e| format!("Query error: {}", e))?;

    let mut recordings = Vec::new();
    for recording in recording_iter {
        recordings.push(recording.map_err(|e| format!("Row error: {}", e))?);
    }

    println!("✅ Retrieved {} recordings", recordings.len());
    Ok(recordings)
}

#[tauri::command]
async fn delete_recording(id: i64) -> Result<(), String> {
    println!("🗑️ Deleting recording with ID: {}", id);

    let conn = init_database().map_err(|e| format!("Database error: {}", e))?;

    let result = conn.execute("DELETE FROM recordings WHERE id = ?1", [id]);

    match result {
        Ok(rows_affected) => {
            if rows_affected > 0 {
                println!("✅ Recording deleted successfully");
                Ok(())
            } else {
                Err("Recording not found".to_string())
            }
        }
        Err(e) => {
            println!("❌ Failed to delete recording: {}", e);
            Err(format!("Failed to delete recording: {}", e))
        }
    }
}

// Einstellungen-Funktionen
fn get_settings_path() -> Result<PathBuf, String> {
    let mut path = std::env::current_dir()
        .map_err(|e| format!("Failed to get current directory: {}", e))?;
    path.push("app_data");
    path.push("settings.json");
    Ok(path)
}

#[tauri::command]
async fn save_settings(settings: AppSettings) -> Result<(), String> {
    println!("💾 Saving app settings...");

    let settings_path = get_settings_path()?;

    // Erstelle Verzeichnis falls es nicht existiert
    if let Some(parent) = settings_path.parent() {
        fs::create_dir_all(parent).map_err(|e| format!("Failed to create settings directory: {}", e))?;
    }

    let settings_json = serde_json::to_string_pretty(&settings)
        .map_err(|e| format!("Failed to serialize settings: {}", e))?;

    fs::write(&settings_path, settings_json)
        .map_err(|e| format!("Failed to write settings file: {}", e))?;

    println!("✅ Settings saved to: {:?}", settings_path);
    Ok(())
}

#[tauri::command]
async fn load_settings() -> Result<AppSettings, String> {
    println!("📋 Loading app settings...");

    let settings_path = get_settings_path()?;

    if !settings_path.exists() {
        println!("⚠️ Settings file not found, using defaults");
        return Ok(AppSettings::default());
    }

    let settings_content = fs::read_to_string(&settings_path)
        .map_err(|e| format!("Failed to read settings file: {}", e))?;

    let settings: AppSettings = serde_json::from_str(&settings_content)
        .map_err(|e| format!("Failed to parse settings: {}", e))?;

    println!("✅ Settings loaded from: {:?}", settings_path);
    Ok(settings)
}

#[tauri::command]
async fn get_cost_stats() -> Result<CostStats, String> {
    println!("💰 Getting cost statistics...");

    let conn = init_database().map_err(|e| format!("Database error: {}", e))?;

    let now = Utc::now();
    let today_start = now.date_naive().and_hms_opt(0, 0, 0).unwrap().and_utc();
    let week_start = now - chrono::Duration::days(7);
    let month_start = now - chrono::Duration::days(30);

    // Heute
    let mut stmt = conn.prepare(
        "SELECT SUM(cost_usd), COUNT(*) FROM recordings WHERE timestamp >= ?1"
    ).map_err(|e| format!("Prepare error: {}", e))?;

    let (today_cost, today_recordings): (Option<f64>, i32) = stmt.query_row(
        [today_start.to_rfc3339()],
        |row| Ok((row.get(0)?, row.get(1)?))
    ).map_err(|e| format!("Query error: {}", e))?;

    // Diese Woche
    let mut stmt = conn.prepare(
        "SELECT SUM(cost_usd), COUNT(*) FROM recordings WHERE timestamp >= ?1"
    ).map_err(|e| format!("Prepare error: {}", e))?;

    let (week_cost, week_recordings): (Option<f64>, i32) = stmt.query_row(
        [week_start.to_rfc3339()],
        |row| Ok((row.get(0)?, row.get(1)?))
    ).map_err(|e| format!("Query error: {}", e))?;

    // Dieser Monat
    let mut stmt = conn.prepare(
        "SELECT SUM(cost_usd), COUNT(*) FROM recordings WHERE timestamp >= ?1"
    ).map_err(|e| format!("Prepare error: {}", e))?;

    let (month_cost, month_recordings): (Option<f64>, i32) = stmt.query_row(
        [month_start.to_rfc3339()],
        |row| Ok((row.get(0)?, row.get(1)?))
    ).map_err(|e| format!("Query error: {}", e))?;

    // Gesamt
    let mut stmt = conn.prepare(
        "SELECT SUM(cost_usd), COUNT(*) FROM recordings"
    ).map_err(|e| format!("Prepare error: {}", e))?;

    let (total_cost, total_recordings): (Option<f64>, i32) = stmt.query_row(
        [],
        |row| Ok((row.get(0)?, row.get(1)?))
    ).map_err(|e| format!("Query error: {}", e))?;

    // Teuerste Aufnahme heute
    let mut stmt = conn.prepare(
        "SELECT id, timestamp, duration_seconds, transcription, cost_usd, file_path
         FROM recordings
         WHERE timestamp >= ?1
         ORDER BY cost_usd DESC
         LIMIT 1"
    ).map_err(|e| format!("Prepare error: {}", e))?;

    let most_expensive_today = stmt.query_row(
        [today_start.to_rfc3339()],
        |row| {
            let timestamp_str: String = row.get(1)?;
            let timestamp = DateTime::parse_from_rfc3339(&timestamp_str)
                .map_err(|_| rusqlite::Error::InvalidColumnType(1, "timestamp".to_string(), rusqlite::types::Type::Text))?
                .with_timezone(&Utc);

            Ok(Recording {
                id: row.get(0)?,
                timestamp,
                duration_seconds: row.get(2)?,
                transcription: row.get(3)?,
                cost_usd: row.get(4)?,
                file_path: row.get(5)?,
            })
        }
    ).ok();

    let stats = CostStats {
        today_cost: today_cost.unwrap_or(0.0),
        week_cost: week_cost.unwrap_or(0.0),
        month_cost: month_cost.unwrap_or(0.0),
        total_cost: total_cost.unwrap_or(0.0),
        today_recordings,
        week_recordings,
        month_recordings,
        total_recordings,
        most_expensive_today,
    };

    println!("✅ Cost stats calculated: Today ${:.4}, Week ${:.4}, Month ${:.4}",
             stats.today_cost, stats.week_cost, stats.month_cost);

    Ok(stats)
}

#[tauri::command]
async fn get_audio_devices() -> Result<AudioDeviceList, String> {
    println!("🎤 Getting available audio devices...");

    // Für jetzt simulieren wir Audio-Geräte
    // In einer echten Implementierung würde man hier das Audio-System abfragen
    let devices = vec![
        AudioDevice {
            id: "default".to_string(),
            name: "Standard-Mikrofon".to_string(),
            is_default: true,
            is_available: true,
        },
        AudioDevice {
            id: "builtin".to_string(),
            name: "Eingebautes Mikrofon".to_string(),
            is_default: false,
            is_available: true,
        },
        AudioDevice {
            id: "usb_mic".to_string(),
            name: "USB-Mikrofon".to_string(),
            is_default: false,
            is_available: false, // Nicht angeschlossen
        },
    ];

    let device_list = AudioDeviceList {
        default_device_id: Some("default".to_string()),
        devices,
    };

    println!("✅ Found {} audio devices", device_list.devices.len());
    Ok(device_list)
}

#[tauri::command]
async fn test_audio_device(device_id: String) -> Result<bool, String> {
    println!("🧪 Testing audio device: {}", device_id);

    // Simuliere Audio-Device Test
    // In einer echten Implementierung würde man hier das Mikrofon testen
    tokio::time::sleep(tokio::time::Duration::from_millis(1000)).await;

    // Simuliere Erfolg für verfügbare Geräte
    let success = !device_id.contains("usb") || device_id == "default";

    if success {
        println!("✅ Audio device test successful: {}", device_id);
    } else {
        println!("❌ Audio device test failed: {}", device_id);
    }

    Ok(success)
}

#[tauri::command]
async fn set_preferred_audio_device(device_id: String) -> Result<(), String> {
    println!("🎤 Setting preferred audio device: {}", device_id);

    // Hier würde man das bevorzugte Audio-Gerät im System setzen
    // Für jetzt loggen wir es nur

    println!("✅ Preferred audio device set to: {}", device_id);
    Ok(())
}

#[tauri::command]
async fn enhance_transcription(request: AIProcessingRequest) -> Result<TranscriptionEnhancement, String> {
    println!("🤖 Enhancing transcription with AI...");
    let start_time = std::time::Instant::now();

    let text = request.text.trim();
    if text.is_empty() {
        return Err("Empty text provided".to_string());
    }

    // Simuliere AI-Processing (in echter App würde hier OpenAI GPT-4 verwendet)
    tokio::time::sleep(tokio::time::Duration::from_millis(500)).await;

    // Text-Enhancement: Entferne Füllwörter und verbessere Interpunktion
    let enhanced_text = enhance_text_quality(text);

    // Generiere Zusammenfassung
    let summary = if request.include_summary {
        Some(generate_summary(&enhanced_text))
    } else {
        None
    };

    // Extrahiere Schlüsselwörter
    let keywords = if request.include_keywords {
        extract_keywords(&enhanced_text)
    } else {
        Vec::new()
    };

    // Erkenne Action Items
    let action_items = if request.include_action_items {
        extract_action_items(&enhanced_text)
    } else {
        Vec::new()
    };

    let processing_time = start_time.elapsed().as_millis() as u64;

    let enhancement = TranscriptionEnhancement {
        original_text: text.to_string(),
        enhanced_text,
        summary,
        keywords,
        action_items,
        confidence_score: 0.92, // Simuliert
        processing_time_ms: processing_time,
    };

    println!("✅ Transcription enhanced in {}ms", processing_time);
    Ok(enhancement)
}

// Hilfsfunktionen für AI-Processing
fn enhance_text_quality(text: &str) -> String {
    let mut enhanced = text.to_string();

    // Entferne häufige Füllwörter
    let filler_words = ["äh", "ähm", "also", "halt", "irgendwie", "sozusagen"];
    for word in filler_words {
        enhanced = enhanced.replace(&format!(" {} ", word), " ");
        enhanced = enhanced.replace(&format!("{} ", word), "");
    }

    // Verbessere Interpunktion
    enhanced = enhanced.replace(" .", ".");
    enhanced = enhanced.replace(" ,", ",");
    enhanced = enhanced.replace(" ?", "?");
    enhanced = enhanced.replace(" !", "!");

    // Erste Buchstabe groß
    if let Some(first_char) = enhanced.chars().next() {
        enhanced = first_char.to_uppercase().collect::<String>() + &enhanced[1..];
    }

    // Sätze mit Punkt beenden wenn nötig
    if !enhanced.ends_with('.') && !enhanced.ends_with('!') && !enhanced.ends_with('?') {
        enhanced.push('.');
    }

    enhanced
}

fn generate_summary(text: &str) -> String {
    if text.len() < 50 {
        return text.to_string();
    }

    // Einfache Zusammenfassung: Erste 100 Zeichen + wichtigste Wörter
    let words: Vec<&str> = text.split_whitespace().collect();
    let important_words: Vec<&str> = words.iter()
        .filter(|word| word.len() > 4 && !["diese", "haben", "werden", "können", "sollen"].contains(word))
        .take(5)
        .copied()
        .collect();

    if words.len() > 15 {
        format!("Zusammenfassung: {}... Schlüsselthemen: {}",
                words[..10].join(" "),
                important_words.join(", "))
    } else {
        text.to_string()
    }
}

fn extract_keywords(text: &str) -> Vec<String> {
    let words: Vec<&str> = text.split_whitespace().collect();
    let mut keywords = Vec::new();

    // Finde wichtige Wörter (länger als 4 Zeichen, keine Füllwörter)
    for word in words {
        let clean_word = word.trim_matches(|c: char| !c.is_alphabetic()).to_lowercase();
        if clean_word.len() > 4
            && !["diese", "haben", "werden", "können", "sollen", "möchte", "würde"].contains(&clean_word.as_str())
            && !keywords.contains(&clean_word) {
            keywords.push(clean_word);
        }
    }

    keywords.truncate(8); // Maximal 8 Keywords
    keywords
}

fn extract_action_items(text: &str) -> Vec<String> {
    let mut action_items = Vec::new();
    let sentences: Vec<&str> = text.split(&['.', '!', '?'][..]).collect();

    // Suche nach Action-Indikatoren
    let action_indicators = ["muss", "soll", "wird", "todo", "aufgabe", "erledigen", "machen", "bis"];

    for sentence in sentences {
        let sentence = sentence.trim();
        if sentence.len() > 10 {
            for indicator in action_indicators {
                if sentence.to_lowercase().contains(indicator) {
                    action_items.push(format!("• {}", sentence));
                    break;
                }
            }
        }
    }

    action_items.truncate(5); // Maximal 5 Action Items
    action_items
}

#[tauri::command]
async fn read_audio_file(file_path: String) -> Result<Vec<u8>, String> {
    println!("📁 Reading audio file: {}", file_path);

    // Prüfe ob die Datei existiert
    if !std::path::Path::new(&file_path).exists() {
        return Err(format!("Audio file not found: {}", file_path));
    }

    match std::fs::read(&file_path) {
        Ok(data) => {
            println!("✅ Audio file read successfully, size: {} bytes", data.len());
            Ok(data)
        }
        Err(e) => {
            println!("❌ Failed to read audio file: {}", e);
            Err(format!("Failed to read audio file: {}", e))
        }
    }
}

fn main() {
    let audio_state = AudioStateArc::new(Mutex::new(AudioState {
        is_recording: false,
        audio_data: Vec::new(),
    }));

    tauri::Builder::default()
        .plugin(tauri_plugin_mic_recorder::init())
        .manage(audio_state)
        .setup(|app| {
            println!("🚀 MeinWort App setup complete with mic-recorder plugin");
            Ok(())
        })
        .invoke_handler(tauri::generate_handler![
            start_native_recording,
            stop_native_recording,
            check_microphone_access,
            start_window_drag,
            read_audio_file,
            save_recording,
            get_recordings_history,
            delete_recording,
            save_settings,
            load_settings,
            get_cost_stats,
            get_audio_devices,
            test_audio_device,
            set_preferred_audio_device,
            enhance_transcription
        ])
        .run(tauri::generate_context!())
        .expect("error while running tauri application");
}