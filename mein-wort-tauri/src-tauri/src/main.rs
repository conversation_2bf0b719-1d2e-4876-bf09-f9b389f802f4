// Prevents additional console window on Windows in release, DO NOT REMOVE!!
#![cfg_attr(not(debug_assertions), windows_subsystem = "windows")]

use tauri::{Manager, Window};
use std::sync::{Arc, Mutex};

// Audio state management für Future Verwendung
struct AudioState {
    is_recording: bool,
    audio_data: Vec<f32>,
}

type AudioStateArc = Arc<Mutex<AudioState>>;

#[tauri::command]
async fn start_native_recording(_state: tauri::State<'_, AudioStateArc>) -> Result<String, String> {
    println!("🎤 Native audio recording start requested");
    
    // Plugin-basierte Aufnahme wird über Frontend gesteuert
    Ok("Native recording ready - use plugin commands".to_string())
}

#[tauri::command]
async fn stop_native_recording(_state: tauri::State<'_, AudioStateArc>) -> Result<String, String> {
    println!("🛑 Native audio recording stop requested");
    
    // Plugin-basierte Aufnahme wird über Frontend gesteuert
    Ok("Native recording stopped - use plugin commands".to_string())
}

#[tauri::command]
async fn check_microphone_access() -> Result<String, String> {
    println!("🔍 Checking microphone access...");
    
    // Plugin wird Berechtigung automatisch anfragen
    Ok("Microphone access will be requested by plugin".to_string())
}

#[tauri::command]
async fn start_window_drag(window: Window) -> Result<(), String> {
    window.start_dragging().map_err(|e| e.to_string())
}

#[tauri::command]
async fn read_audio_file(file_path: String) -> Result<Vec<u8>, String> {
    println!("📁 Reading audio file: {}", file_path);

    match std::fs::read(&file_path) {
        Ok(data) => {
            println!("✅ Audio file read successfully, size: {} bytes", data.len());
            Ok(data)
        }
        Err(e) => {
            println!("❌ Failed to read audio file: {}", e);
            Err(format!("Failed to read audio file: {}", e))
        }
    }
}

fn main() {
    let audio_state = AudioStateArc::new(Mutex::new(AudioState {
        is_recording: false,
        audio_data: Vec::new(),
    }));

    tauri::Builder::default()
        .plugin(tauri_plugin_mic_recorder::init())
        .manage(audio_state)
        .setup(|app| {
            println!("🚀 MeinWort App setup complete with mic-recorder plugin");
            Ok(())
        })
        .invoke_handler(tauri::generate_handler![
            start_native_recording,
            stop_native_recording,
            check_microphone_access,
            start_window_drag,
            read_audio_file
        ])
        .run(tauri::generate_context!())
        .expect("error while running tauri application");
}