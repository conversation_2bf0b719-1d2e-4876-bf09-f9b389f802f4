{"$schema": "../node_modules/@tauri-apps/cli/schema.json", "productName": "MeinWort", "version": "../package.json", "identifier": "studio.raumblick.meinwort", "build": {"frontendDist": "../", "devUrl": "http://localhost:1420"}, "app": {"macOSPrivateApi": true, "withGlobalTauri": true, "windows": [{"title": "<PERSON><PERSON><PERSON><PERSON> - Speech to Text Assistant", "width": 600, "height": 400, "resizable": true, "transparent": true, "decorations": false, "alwaysOnTop": true, "skipTaskbar": true, "center": true, "titleBarStyle": "Overlay", "hiddenTitle": true, "shadow": false, "dragDropEnabled": false}], "security": {"csp": null, "capabilities": ["window-dragging", "microphone-recording"]}}, "bundle": {"active": true, "targets": "all", "icon": [], "copyright": "", "category": "Productivity", "shortDescription": "AI-powered speech to text assistant", "longDescription": "<PERSON><PERSON><PERSON><PERSON> is an intelligent speech-to-text assistant with contextual AI enhancement for seamless voice-to-text conversion.", "macOS": {"frameworks": [], "minimumSystemVersion": "", "exceptionDomain": "", "entitlements": "entitlements.plist"}}, "plugins": {"mic-recorder": {"permissions": ["allow-start-recording", "allow-stop-recording"]}}}