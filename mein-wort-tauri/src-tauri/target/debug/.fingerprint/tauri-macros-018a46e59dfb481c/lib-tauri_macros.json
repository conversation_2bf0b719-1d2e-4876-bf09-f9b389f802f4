{"rustc": 15497389221046826682, "features": "[\"compression\"]", "declared_features": "[\"compression\", \"config-json5\", \"config-toml\", \"custom-protocol\", \"isolation\", \"tracing\"]", "target": 4649449654257170297, "profile": 3033921117576893, "path": 183773806448630647, "deps": [[3060637413840920116, "proc_macro2", false, 14948573736911583313], [7341521034400937459, "tauri_codegen", false, 11303626335116164371], [11050281405049894993, "tauri_utils", false, 13105931267000994788], [13077543566650298139, "heck", false, 10422705245453910707], [17990358020177143287, "quote", false, 6859924821721552913], [18149961000318489080, "syn", false, 17516338615621841399]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/tauri-macros-018a46e59dfb481c/dep-lib-tauri_macros", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}