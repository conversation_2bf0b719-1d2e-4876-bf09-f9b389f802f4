{"rustc": 15497389221046826682, "features": "[\"any_impl\", \"default\", \"miniz_oxide\", \"rust_backend\"]", "declared_features": "[\"any_impl\", \"any_zlib\", \"cloudflare-zlib-sys\", \"cloudflare_zlib\", \"default\", \"libz-ng-sys\", \"libz-rs-sys\", \"libz-sys\", \"miniz-sys\", \"miniz_oxide\", \"rust_backend\", \"zlib\", \"zlib-default\", \"zlib-ng\", \"zlib-ng-compat\", \"zlib-rs\"]", "target": 6173716359330453699, "profile": 3033921117576893, "path": 12176275440082422398, "deps": [[4675849561795547236, "miniz_oxide", false, 1827136147413731928], [5466618496199522463, "crc32fast", false, 925318807585182128]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/flate2-0f7f07dab3e82b48/dep-lib-flate2", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}