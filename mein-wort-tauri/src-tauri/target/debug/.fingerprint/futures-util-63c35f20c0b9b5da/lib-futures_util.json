{"rustc": 15497389221046826682, "features": "[\"alloc\", \"async-await\", \"async-await-macro\", \"default\", \"futures-macro\", \"slab\", \"std\"]", "declared_features": "[\"alloc\", \"async-await\", \"async-await-macro\", \"bilock\", \"cfg-target-has-atomic\", \"channel\", \"compat\", \"default\", \"futures-channel\", \"futures-io\", \"futures-macro\", \"futures-sink\", \"futures_01\", \"io\", \"io-compat\", \"memchr\", \"portable-atomic\", \"sink\", \"slab\", \"std\", \"tokio-io\", \"unstable\", \"write-all-vectored\"]", "target": 1788798584831431502, "profile": 336243669335521001, "path": 3788016722654844256, "deps": [[1615478164327904835, "pin_utils", false, 3933048626433655000], [1906322745568073236, "pin_project_lite", false, 7201759893766850000], [6955678925937229351, "slab", false, 14586278559121947517], [7620660491849607393, "futures_core", false, 11564478866203940291], [10565019901765856648, "futures_macro", false, 15631527175023845766], [16240732885093539806, "futures_task", false, 13233160086506500685]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/futures-util-63c35f20c0b9b5da/dep-lib-futures_util", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}