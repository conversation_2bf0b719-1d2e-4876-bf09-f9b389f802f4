{"rustc": 15497389221046826682, "features": "[\"macos-private-api\"]", "declared_features": "[\"devtools\", \"macos-private-api\"]", "target": 10306386172444932100, "profile": 8276155916380437441, "path": 5434334439908282410, "deps": [[442785307232013896, "build_script_build", false, 8568648380193537501], [3150220818285335163, "url", false, 6486837830076175266], [4143744114649553716, "raw_window_handle", false, 5808669178667259139], [7606335748176206944, "dpi", false, 7028021057856555472], [9010263965687315507, "http", false, 7471525469376270398], [9689903380558560274, "serde", false, 15389414506496549533], [10806645703491011684, "thiserror", false, 14124874787951812739], [11050281405049894993, "tauri_utils", false, 6717577908270330379], [15367738274754116744, "serde_json", false, 11076582103850396275], [16727543399706004146, "cookie", false, 9097389533366088060]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/tauri-runtime-3b52e3a3277ff1d0/dep-lib-tauri_runtime", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}