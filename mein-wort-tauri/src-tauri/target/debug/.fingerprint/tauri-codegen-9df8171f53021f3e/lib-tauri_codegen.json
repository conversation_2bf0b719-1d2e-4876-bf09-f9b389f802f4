{"rustc": 15497389221046826682, "features": "[\"brotli\", \"compression\"]", "declared_features": "[\"brotli\", \"compression\", \"config-json5\", \"config-toml\", \"isolation\", \"regex\"]", "target": 17460618180909919773, "profile": 3033921117576893, "path": 10432949688654511053, "deps": [[3060637413840920116, "proc_macro2", false, 14948573736911583313], [3150220818285335163, "url", false, 3337335275387403867], [4767930184903566869, "plist", false, 3720634866937172836], [4899080583175475170, "semver", false, 11902930428071454877], [7170110829644101142, "json_patch", false, 10962124415035335060], [7392050791754369441, "ico", false, 2569329486341860057], [8319709847752024821, "uuid", false, 10155911677234571809], [9689903380558560274, "serde", false, 987821781024638707], [9857275760291862238, "sha2", false, 9942885282666666024], [10806645703491011684, "thiserror", false, 5609819403022986341], [11050281405049894993, "tauri_utils", false, 6938205978361985344], [12409575957772518135, "time", false, 17664711229057871434], [12687914511023397207, "png", false, 3726131993545148576], [13077212702700853852, "base64", false, 912353896625854956], [14132538657330703225, "brotli", false, 18299290468625298726], [15367738274754116744, "serde_json", false, 2985331687060998168], [15622660310229662834, "walkdir", false, 13731819960054273933], [17990358020177143287, "quote", false, 6859924821721552913], [18149961000318489080, "syn", false, 17516338615621841399]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/tauri-codegen-9df8171f53021f3e/dep-lib-tauri_codegen", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}