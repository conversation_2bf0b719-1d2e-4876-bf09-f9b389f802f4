{"rustc": 15497389221046826682, "features": "[]", "declared_features": "[\"jack\"]", "target": 18313950295211467291, "profile": 8276155916380437441, "path": 13668269130677092417, "deps": [[4145044980200903552, "build_script_build", false, 5807930821246597782], [9689903380558560274, "serde", false, 15389414506496549533], [9897246384292347999, "chrono", false, 7434023132511041212], [10755362358622467486, "tauri", false, 1409784139100519729], [10806645703491011684, "thiserror", false, 14124874787951812739], [11468999454667189829, "clap", false, 10742796982807528685], [14425588341765822814, "cpal", false, 13484678341345536005], [16696992991567834322, "hound", false, 14857937184372016866]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/tauri-plugin-mic-recorder-8567c3dd5f535062/dep-lib-tauri_plugin_mic_recorder", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}