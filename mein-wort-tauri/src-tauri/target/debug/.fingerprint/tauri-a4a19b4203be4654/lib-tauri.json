{"rustc": 15497389221046826682, "features": "[\"common-controls-v6\", \"compression\", \"default\", \"macos-private-api\", \"tauri-runtime-wry\", \"webkit2gtk\", \"webview2-com\", \"wry\"]", "declared_features": "[\"common-controls-v6\", \"compression\", \"config-json5\", \"config-toml\", \"custom-protocol\", \"data-url\", \"default\", \"devtools\", \"http-range\", \"image\", \"image-ico\", \"image-png\", \"isolation\", \"linux-libxdo\", \"macos-private-api\", \"macos-proxy\", \"native-tls\", \"native-tls-vendored\", \"objc-exception\", \"process-relaunch-dangerous-allow-symlink-macos\", \"protocol-asset\", \"rustls-tls\", \"specta\", \"tauri-runtime-wry\", \"test\", \"tracing\", \"tray-icon\", \"unstable\", \"uuid\", \"webkit2gtk\", \"webview-data-url\", \"webview2-com\", \"wry\"]", "target": 12223948975794516716, "profile": 8276155916380437441, "path": 13596148587733741571, "deps": [[40386456601120721, "percent_encoding", false, 3688707677811808193], [442785307232013896, "tauri_runtime", false, 668770526620251094], [1200537532907108615, "url<PERSON><PERSON>n", false, 17033930138029433105], [1386409696764982933, "objc2", false, 7464826260965727412], [3150220818285335163, "url", false, 6486837830076175266], [4143744114649553716, "raw_window_handle", false, 5808669178667259139], [4341921533227644514, "muda", false, 16494228203087300626], [4767930184903566869, "plist", false, 3574681986567558124], [4919829919303820331, "serialize_to_javascript", false, 14535620135051030694], [5986029879202738730, "log", false, 15353946661413452617], [7752760652095876438, "tauri_runtime_wry", false, 8820146692356793893], [8589231650440095114, "embed_plist", false, 9257184391987048233], [9010263965687315507, "http", false, 7471525469376270398], [9228235415475680086, "tauri_macros", false, 10227399388976385094], [9538054652646069845, "tokio", false, 4258206740947524002], [9689903380558560274, "serde", false, 15389414506496549533], [9859211262912517217, "objc2_foundation", false, 10868444889871828647], [9920160576179037441, "getrandom", false, 8370332867683581950], [10229185211513642314, "mime", false, 12713528008489504598], [10575598148575346675, "objc2_app_kit", false, 17085800774680825330], [10629569228670356391, "futures_util", false, 7721170836512157569], [10755362358622467486, "build_script_build", false, 6002307823169475155], [10806645703491011684, "thiserror", false, 14124874787951812739], [11050281405049894993, "tauri_utils", false, 6717577908270330379], [11989259058781683633, "dunce", false, 11549806776433783993], [12565293087094287914, "window_vibrancy", false, 9071329452413333222], [12986574360607194341, "serde_repr", false, 3497649956509626271], [13077543566650298139, "heck", false, 14552747480726017451], [13625485746686963219, "anyhow", false, 16185852120038536043], [15367738274754116744, "serde_json", false, 11076582103850396275], [16928111194414003569, "dirs", false, 13770779022959820294], [17155886227862585100, "glob", false, 11999561895933821652]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/tauri-a4a19b4203be4654/dep-lib-tauri", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}