{"rustc": 15497389221046826682, "features": "[\"ahash\", \"allocator-api2\", \"default\", \"inline-more\"]", "declared_features": "[\"ahash\", \"alloc\", \"allocator-api2\", \"compiler_builtins\", \"core\", \"default\", \"equivalent\", \"inline-more\", \"nightly\", \"raw\", \"rayon\", \"rkyv\", \"rustc-dep-of-std\", \"rustc-internal-api\", \"serde\"]", "target": 9101038166729729440, "profile": 8276155916380437441, "path": 16683556435628342865, "deps": [[966925859616469517, "ahash", false, 13061482571009358532], [9150530836556604396, "allocator_api2", false, 4664733190181921114]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/hashbrown-7631fb32179b979f/dep-lib-hashbrown", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}