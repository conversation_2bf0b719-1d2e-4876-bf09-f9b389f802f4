{"rustc": 15497389221046826682, "features": "[\"config-json\", \"default\"]", "declared_features": "[\"codegen\", \"config-json\", \"config-json5\", \"config-toml\", \"default\", \"isolation\", \"quote\", \"tauri-codegen\"]", "target": 1006236803848883740, "profile": 3033921117576893, "path": 4262836781890506995, "deps": [[4899080583175475170, "semver", false, 11902930428071454877], [6913375703034175521, "schemars", false, 6917671937661051798], [7170110829644101142, "json_patch", false, 10962124415035335060], [8786711029710048183, "toml", false, 14348999470944116303], [9689903380558560274, "serde", false, 987821781024638707], [11050281405049894993, "tauri_utils", false, 6938205978361985344], [12714016054753183456, "tauri_winres", false, 2450272544556513405], [13077543566650298139, "heck", false, 6665897030291015629], [13475171727366188400, "cargo_toml", false, 4443475725716144447], [13625485746686963219, "anyhow", false, 6550937551417288592], [15367738274754116744, "serde_json", false, 2985331687060998168], [15622660310229662834, "walkdir", false, 13731819960054273933], [16928111194414003569, "dirs", false, 13967990436975853658], [17155886227862585100, "glob", false, 17543998344504681454]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/tauri-build-11c125ac79011f3a/dep-lib-tauri_build", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}