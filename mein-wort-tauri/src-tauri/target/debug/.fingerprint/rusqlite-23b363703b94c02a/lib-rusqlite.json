{"rustc": 15497389221046826682, "features": "[\"bundled\", \"modern_sqlite\"]", "declared_features": "[\"array\", \"backup\", \"blob\", \"buildtime_bindgen\", \"bundled\", \"bundled-full\", \"bundled-sqlcipher\", \"bundled-sqlcipher-vendored-openssl\", \"bundled-windows\", \"chrono\", \"collation\", \"column_decltype\", \"csv\", \"csvtab\", \"extra_check\", \"functions\", \"hooks\", \"i128_blob\", \"in_gecko\", \"limits\", \"load_extension\", \"modern-full\", \"modern_sqlite\", \"release_memory\", \"serde_json\", \"series\", \"session\", \"sqlcipher\", \"time\", \"trace\", \"unlock_notify\", \"url\", \"uuid\", \"vtab\", \"wasm32-wasi-vfs\", \"window\", \"winsqlite3\", \"with-asan\"]", "target": 1382081207361365847, "profile": 5347358027863023418, "path": 4940982305984911195, "deps": [[1067686709987796982, "libsqlite3_sys", false, 2825280544214957739], [3405817021026194662, "hashlink", false, 14627051013266875987], [5510864063823219921, "fallible_streaming_iterator", false, 5921771826639758083], [6048213226671835012, "smallvec", false, 8208523433493346995], [7896293946984509699, "bitflags", false, 933365640266482348], [17725626451704002459, "fallible_iterator", false, 7167486300830494372]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/rusqlite-23b363703b94c02a/dep-lib-rusqlite", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}