{"rustc": 15497389221046826682, "features": "[\"alloc\"]", "declared_features": "[\"alloc\", \"default\", \"fresh-rust\", \"nightly\", \"serde\", \"std\"]", "target": 5388200169723499962, "profile": 2933120001108589360, "path": 10614249300049947951, "deps": [], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/allocator-api2-64e07048c06407e7/dep-lib-allocator_api2", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}