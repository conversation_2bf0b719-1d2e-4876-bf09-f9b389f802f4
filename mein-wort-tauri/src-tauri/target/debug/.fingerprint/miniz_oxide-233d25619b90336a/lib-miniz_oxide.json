{"rustc": 15497389221046826682, "features": "[\"default\", \"simd\", \"simd-adler32\", \"with-alloc\"]", "declared_features": "[\"alloc\", \"block-boundary\", \"compiler_builtins\", \"core\", \"default\", \"rustc-dep-of-std\", \"serde\", \"simd\", \"simd-adler32\", \"std\", \"with-alloc\"]", "target": 8661567070972402511, "profile": 7103650835353764149, "path": 10485004818562536637, "deps": [[4018467389006652250, "simd_adler32", false, 3970121865314287043], [15407850927583745935, "adler2", false, 12297141760728049501]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/miniz_oxide-233d25619b90336a/dep-lib-miniz_oxide", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}