{"rustc": 15497389221046826682, "features": "", "declared_features": "", "target": 0, "profile": 0, "path": 0, "deps": [[5921216119076210821, "build_script_build", false, 2052897685726213221], [10755362358622467486, "build_script_build", false, 5672748119918731162], [4145044980200903552, "build_script_build", false, 10473892732662655970]], "local": [{"RerunIfChanged": {"output": "debug/build/mein-wort-tauri-29deda1a57ff05d4/output", "paths": ["tauri.conf.json", "capabilities"]}}, {"RerunIfEnvChanged": {"var": "TAURI_CONFIG", "val": null}}, {"RerunIfEnvChanged": {"var": "REMOVE_UNUSED_COMMANDS", "val": null}}], "rustflags": [], "config": 0, "compile_kind": 0}