{"rustc": 15497389221046826682, "features": "[\"macos-private-api\"]", "declared_features": "[\"devtools\", \"macos-private-api\", \"macos-proxy\", \"objc-exception\", \"tracing\", \"unstable\"]", "target": 1901661049345253480, "profile": 5347358027863023418, "path": 2253429996432136783, "deps": [[442785307232013896, "tauri_runtime", false, 17476699458984737215], [1386409696764982933, "objc2", false, 3416124414745136707], [3150220818285335163, "url", false, 1575297935110896344], [4143744114649553716, "raw_window_handle", false, 11673856966557422961], [5986029879202738730, "log", false, 5938598544286453029], [7752760652095876438, "build_script_build", false, 11210257341927190633], [9010263965687315507, "http", false, 12058107425905163915], [9859211262912517217, "objc2_foundation", false, 12255271202417510079], [10575598148575346675, "objc2_app_kit", false, 606580607435188803], [11050281405049894993, "tauri_utils", false, 18298302778007802746], [13223659721939363523, "tao", false, 16218235299499322907], [14794439852947137341, "wry", false, 7179256382604603456]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/tauri-runtime-wry-26fbf2ddab8f8832/dep-lib-tauri_runtime_wry", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}