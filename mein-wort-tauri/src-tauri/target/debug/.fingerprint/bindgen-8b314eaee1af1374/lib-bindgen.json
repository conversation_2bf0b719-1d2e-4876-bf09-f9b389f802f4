{"rustc": 15497389221046826682, "features": "[\"runtime\"]", "declared_features": "[\"__cli\", \"__testing_only_extra_assertions\", \"__testing_only_libclang_16\", \"__testing_only_libclang_9\", \"default\", \"experimental\", \"logging\", \"prettyplease\", \"runtime\", \"static\", \"which-rustfmt\"]", "target": 15460903241111225995, "profile": 3033921117576893, "path": 9028257035232177878, "deps": [[950716570147248582, "cexpr", false, 15769380079554501329], [3060637413840920116, "proc_macro2", false, 14948573736911583313], [3317542222502007281, "itertools", false, 8143527648686777716], [4885725550624711673, "clang_sys", false, 15327083734339415183], [7896293946984509699, "bitflags", false, 1322565773106617413], [8410525223747752176, "shlex", false, 9630732877516880730], [9451456094439810778, "regex", false, 2199860576341472590], [14288973941860549443, "build_script_build", false, 10597570729341699541], [16055916053474393816, "rustc_hash", false, 5442287961381123917], [17990358020177143287, "quote", false, 6859924821721552913], [18149961000318489080, "syn", false, 17516338615621841399]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/bindgen-8b314eaee1af1374/dep-lib-bindgen", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}