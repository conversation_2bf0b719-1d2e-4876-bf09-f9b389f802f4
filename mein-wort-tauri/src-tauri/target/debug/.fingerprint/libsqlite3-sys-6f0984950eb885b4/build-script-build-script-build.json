{"rustc": 15497389221046826682, "features": "[\"bundled\", \"bundled_bindings\", \"cc\", \"default\", \"min_sqlite_version_3_14_0\", \"pkg-config\", \"vcpkg\"]", "declared_features": "[\"bindgen\", \"buildtime_bindgen\", \"bundled\", \"bundled-sqlcipher\", \"bundled-sqlcipher-vendored-openssl\", \"bundled-windows\", \"bundled_bindings\", \"cc\", \"default\", \"in_gecko\", \"min_sqlite_version_3_14_0\", \"openssl-sys\", \"pkg-config\", \"preupdate_hook\", \"session\", \"sqlcipher\", \"unlock_notify\", \"vcpkg\", \"wasm32-wasi-vfs\", \"winsqlite3\", \"with-asan\"]", "target": 17883862002600103897, "profile": 3033921117576893, "path": 1898233263084864777, "deps": [[705192069995309102, "cc", false, 6884286039790036129], [3214373357989284387, "pkg_config", false, 16175185569034893719], [12933202132622624734, "vcpkg", false, 6141675351307851662]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/libsqlite3-sys-6f0984950eb885b4/dep-build-script-build-script-build", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}