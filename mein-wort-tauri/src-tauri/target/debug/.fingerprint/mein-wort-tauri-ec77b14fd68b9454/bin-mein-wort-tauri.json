{"rustc": 15497389221046826682, "features": "[]", "declared_features": "[\"custom-protocol\"]", "target": 6012835892673671339, "profile": 2330448797067240312, "path": 4942398508502643691, "deps": [[4145044980200903552, "tauri_plugin_mic_recorder", false, 9147880169178872674], [5921216119076210821, "build_script_build", false, 2614899077514056604], [8324132117207348776, "rusqlite", false, 652800382335648764], [9538054652646069845, "tokio", false, 4258206740947524002], [9689903380558560274, "serde", false, 15389414506496549533], [9897246384292347999, "chrono", false, 7434023132511041212], [10755362358622467486, "tauri", false, 1409784139100519729], [15367738274754116744, "serde_json", false, 11076582103850396275]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/mein-wort-tauri-ec77b14fd68b9454/dep-bin-mein-wort-tauri", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}