{"rustc": 15497389221046826682, "features": "[\"bundled\", \"modern_sqlite\"]", "declared_features": "[\"array\", \"backup\", \"blob\", \"buildtime_bindgen\", \"bundled\", \"bundled-full\", \"bundled-sqlcipher\", \"bundled-sqlcipher-vendored-openssl\", \"bundled-windows\", \"chrono\", \"collation\", \"column_decltype\", \"csv\", \"csvtab\", \"extra_check\", \"functions\", \"hooks\", \"i128_blob\", \"in_gecko\", \"limits\", \"load_extension\", \"modern-full\", \"modern_sqlite\", \"release_memory\", \"serde_json\", \"series\", \"session\", \"sqlcipher\", \"time\", \"trace\", \"unlock_notify\", \"url\", \"uuid\", \"vtab\", \"wasm32-wasi-vfs\", \"window\", \"winsqlite3\", \"with-asan\"]", "target": 1382081207361365847, "profile": 8276155916380437441, "path": 4940982305984911195, "deps": [[1067686709987796982, "libsqlite3_sys", false, 7511052718235473281], [3405817021026194662, "hashlink", false, 11471014933117922112], [5510864063823219921, "fallible_streaming_iterator", false, 18137238562559645022], [6048213226671835012, "smallvec", false, 4466197378271605700], [7896293946984509699, "bitflags", false, 4246233706020237366], [17725626451704002459, "fallible_iterator", false, 9161043665751132316]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/rusqlite-ade96a35da21bb29/dep-lib-rusqlite", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}