{"rustc": 15497389221046826682, "features": "[]", "declared_features": "[\"asio\", \"asio-sys\", \"jack\", \"num-traits\", \"oboe-shared-stdcxx\", \"wasm-bindgen\"]", "target": 12729655241696202585, "profile": 8276155916380437441, "path": 7494400712319544956, "deps": [[11515236608402904465, "mach2", false, 12861928588847583959], [12589608519315293066, "core_foundation_sys", false, 7922445334561151688], [13164455933698855687, "<PERSON><PERSON><PERSON>", false, 11190023686689075052], [14425588341765822814, "build_script_build", false, 7101298255208152250], [17999592116325259774, "dasp_sample", false, 14197491786189204686]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/cpal-ab56705864085973/dep-lib-cpal", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}