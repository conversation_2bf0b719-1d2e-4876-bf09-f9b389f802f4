{"rustc": 15497389221046826682, "features": "[\"config-json\", \"default\"]", "declared_features": "[\"codegen\", \"config-json\", \"config-json5\", \"config-toml\", \"default\", \"isolation\", \"quote\", \"tauri-codegen\"]", "target": 1006236803848883740, "profile": 3033921117576893, "path": 4262836781890506995, "deps": [[4899080583175475170, "semver", false, 11902930428071454877], [6913375703034175521, "schemars", false, 9718118575676667637], [7170110829644101142, "json_patch", false, 11846860090398662896], [8786711029710048183, "toml", false, 3636923464296790111], [9689903380558560274, "serde", false, 987821781024638707], [11050281405049894993, "tauri_utils", false, 13105931267000994788], [12714016054753183456, "tauri_winres", false, 5044693868408486848], [13077543566650298139, "heck", false, 10422705245453910707], [13475171727366188400, "cargo_toml", false, 10467350451118263850], [13625485746686963219, "anyhow", false, 17684233449822154158], [15367738274754116744, "serde_json", false, 768582583343703277], [15622660310229662834, "walkdir", false, 12435053605026983542], [16928111194414003569, "dirs", false, 10327487006046303646], [17155886227862585100, "glob", false, 12278460260972684763]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/tauri-build-ab343c34f230030d/dep-lib-tauri_build", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}