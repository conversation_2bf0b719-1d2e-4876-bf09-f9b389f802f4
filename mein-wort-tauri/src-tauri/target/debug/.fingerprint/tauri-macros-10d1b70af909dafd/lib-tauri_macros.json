{"rustc": 15497389221046826682, "features": "[\"compression\"]", "declared_features": "[\"compression\", \"config-json5\", \"config-toml\", \"custom-protocol\", \"isolation\", \"tracing\"]", "target": 4649449654257170297, "profile": 3033921117576893, "path": 183773806448630647, "deps": [[3060637413840920116, "proc_macro2", false, 14948573736911583313], [7341521034400937459, "tauri_codegen", false, 351377729520156212], [11050281405049894993, "tauri_utils", false, 6938205978361985344], [13077543566650298139, "heck", false, 6665897030291015629], [17990358020177143287, "quote", false, 6859924821721552913], [18149961000318489080, "syn", false, 17516338615621841399]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/tauri-macros-10d1b70af909dafd/dep-lib-tauri_macros", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}