{"rustc": 15497389221046826682, "features": "[\"macos-private-api\"]", "declared_features": "[\"devtools\", \"macos-private-api\"]", "target": 10306386172444932100, "profile": 5347358027863023418, "path": 5434334439908282410, "deps": [[442785307232013896, "build_script_build", false, 8568648380193537501], [3150220818285335163, "url", false, 1575297935110896344], [4143744114649553716, "raw_window_handle", false, 11673856966557422961], [7606335748176206944, "dpi", false, 739217446610387501], [9010263965687315507, "http", false, 12058107425905163915], [9689903380558560274, "serde", false, 1804415665218061737], [10806645703491011684, "thiserror", false, 5609819403022986341], [11050281405049894993, "tauri_utils", false, 18298302778007802746], [15367738274754116744, "serde_json", false, 17832789698231749078], [16727543399706004146, "cookie", false, 6335963322860483386]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/tauri-runtime-1082b70f59b81939/dep-lib-tauri_runtime", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}