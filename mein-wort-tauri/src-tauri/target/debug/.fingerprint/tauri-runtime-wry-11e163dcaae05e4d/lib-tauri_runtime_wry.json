{"rustc": 15497389221046826682, "features": "[\"macos-private-api\"]", "declared_features": "[\"devtools\", \"macos-private-api\", \"macos-proxy\", \"objc-exception\", \"tracing\", \"unstable\"]", "target": 1901661049345253480, "profile": 8276155916380437441, "path": 2253429996432136783, "deps": [[442785307232013896, "tauri_runtime", false, 668770526620251094], [1386409696764982933, "objc2", false, 7464826260965727412], [3150220818285335163, "url", false, 6486837830076175266], [4143744114649553716, "raw_window_handle", false, 5808669178667259139], [5986029879202738730, "log", false, 15353946661413452617], [7752760652095876438, "build_script_build", false, 11210257341927190633], [9010263965687315507, "http", false, 7471525469376270398], [9859211262912517217, "objc2_foundation", false, 10868444889871828647], [10575598148575346675, "objc2_app_kit", false, 17085800774680825330], [11050281405049894993, "tauri_utils", false, 6717577908270330379], [13223659721939363523, "tao", false, 16086832538607653345], [14794439852947137341, "wry", false, 3814348543038590272]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/tauri-runtime-wry-11e163dcaae05e4d/dep-lib-tauri_runtime_wry", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}