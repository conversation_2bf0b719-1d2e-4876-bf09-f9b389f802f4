{"rustc": 15497389221046826682, "features": "[\"brotli\", \"compression\"]", "declared_features": "[\"brotli\", \"compression\", \"config-json5\", \"config-toml\", \"isolation\", \"regex\"]", "target": 17460618180909919773, "profile": 3033921117576893, "path": 10432949688654511053, "deps": [[3060637413840920116, "proc_macro2", false, 14948573736911583313], [3150220818285335163, "url", false, 13085640500749650492], [4767930184903566869, "plist", false, 975980679340598104], [4899080583175475170, "semver", false, 11902930428071454877], [7170110829644101142, "json_patch", false, 11846860090398662896], [7392050791754369441, "ico", false, 1194226206575209730], [8319709847752024821, "uuid", false, 3240745634158239829], [9689903380558560274, "serde", false, 987821781024638707], [9857275760291862238, "sha2", false, 8388753842721076516], [10806645703491011684, "thiserror", false, 1133263765413693886], [11050281405049894993, "tauri_utils", false, 13105931267000994788], [12409575957772518135, "time", false, 7477991770322839957], [12687914511023397207, "png", false, 617430653725771136], [13077212702700853852, "base64", false, 14418199942880491542], [14132538657330703225, "brotli", false, 13786566503350258497], [15367738274754116744, "serde_json", false, 768582583343703277], [15622660310229662834, "walkdir", false, 12435053605026983542], [17990358020177143287, "quote", false, 6859924821721552913], [18149961000318489080, "syn", false, 17516338615621841399]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/tauri-codegen-0493b55dbac0f7ee/dep-lib-tauri_codegen", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}