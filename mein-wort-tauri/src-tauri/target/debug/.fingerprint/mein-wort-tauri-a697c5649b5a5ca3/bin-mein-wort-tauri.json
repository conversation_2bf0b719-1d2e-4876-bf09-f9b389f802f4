{"rustc": 15497389221046826682, "features": "[]", "declared_features": "[\"custom-protocol\"]", "target": 6012835892673671339, "profile": 6675295047989516842, "path": 4942398508502643691, "deps": [[4145044980200903552, "tauri_plugin_mic_recorder", false, 6566287903919972317], [5921216119076210821, "build_script_build", false, 16869819558027465606], [8324132117207348776, "rusqlite", false, 16930981687002877851], [9538054652646069845, "tokio", false, 8961292394750671951], [9689903380558560274, "serde", false, 1804415665218061737], [9897246384292347999, "chrono", false, 16054540185764242307], [10755362358622467486, "tauri", false, 6469089079964339034], [15367738274754116744, "serde_json", false, 17832789698231749078]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/mein-wort-tauri-a697c5649b5a5ca3/dep-bin-mein-wort-tauri", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}