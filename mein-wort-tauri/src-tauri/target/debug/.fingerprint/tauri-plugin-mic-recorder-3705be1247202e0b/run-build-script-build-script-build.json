{"rustc": 15497389221046826682, "features": "", "declared_features": "", "target": 0, "profile": 0, "path": 0, "deps": [[10755362358622467486, "build_script_build", false, 5672748119918731162], [4145044980200903552, "build_script_build", false, 6377676654862461271]], "local": [{"RerunIfChanged": {"output": "debug/build/tauri-plugin-mic-recorder-3705be1247202e0b/output", "paths": ["permissions"]}}, {"RerunIfEnvChanged": {"var": "REMOVE_UNUSED_COMMANDS", "val": null}}], "rustflags": [], "config": 0, "compile_kind": 0}