{"rustc": 15497389221046826682, "features": "", "declared_features": "", "target": 0, "profile": 0, "path": 0, "deps": [[5921216119076210821, "build_script_build", false, 1590862845712328973], [10755362358622467486, "build_script_build", false, 6002307823169475155], [4145044980200903552, "build_script_build", false, 5807930821246597782]], "local": [{"RerunIfChanged": {"output": "debug/build/mein-wort-tauri-e5a84478ee09ee25/output", "paths": ["tauri.conf.json", "capabilities"]}}, {"RerunIfEnvChanged": {"var": "TAURI_CONFIG", "val": null}}, {"RerunIfEnvChanged": {"var": "REMOVE_UNUSED_COMMANDS", "val": null}}], "rustflags": [], "config": 0, "compile_kind": 0}