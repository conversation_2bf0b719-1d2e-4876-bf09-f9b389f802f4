{"microphone-recording": {"identifier": "microphone-recording", "description": "Capability to allow microphone recording functionality", "local": true, "windows": ["main"], "webviews": ["main"], "permissions": ["mic-recorder:default"], "platforms": ["macOS", "windows", "linux"]}, "window-dragging": {"identifier": "window-dragging", "description": "Capability to allow window dragging functionality", "local": true, "windows": ["main"], "webviews": ["main"], "permissions": ["core:window:allow-start-dragging", "core:window:allow-set-position", "core:window:allow-outer-position", "core:window:allow-inner-position"], "platforms": ["macOS", "windows", "linux"]}}