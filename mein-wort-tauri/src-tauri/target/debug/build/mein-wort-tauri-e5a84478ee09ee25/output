cargo:rerun-if-env-changed=TAURI_CONFIG
cargo:rustc-check-cfg=cfg(desktop)
cargo:rustc-cfg=desktop
cargo:rustc-check-cfg=cfg(mobile)
cargo:rerun-if-changed=/Users/<USER>/Desktop/MeinWort/mein-wort-tauri/src-tauri/tauri.conf.json
cargo:rustc-env=TAURI_ANDROID_PACKAGE_NAME_APP_NAME=meinwort
cargo:rustc-env=TAURI_ANDROID_PACKAGE_NAME_PREFIX=studio_raumblick
cargo:rustc-check-cfg=cfg(dev)
cargo:rustc-cfg=dev
cargo:PERMISSION_FILES_PATH=/Users/<USER>/Desktop/MeinWort/mein-wort-tauri/src-tauri/target/debug/build/mein-wort-tauri-e5a84478ee09ee25/out/app-manifest/__app__-permission-files
cargo:rerun-if-changed=capabilities
cargo:rerun-if-env-changed=REMOVE_UNUSED_COMMANDS
cargo:rustc-env=TAURI_ENV_TARGET_TRIPLE=aarch64-apple-darwin
cargo:rustc-env=MACOSX_DEPLOYMENT_TARGET=10.13
