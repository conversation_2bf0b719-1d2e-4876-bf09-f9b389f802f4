[package]
name = "mein-wort-tauri"
version = "0.1.0"
description = "MeinWort - AI Speech to Text Assistant"
authors = ["you"]
license = ""
repository = ""
edition = "2021"

[build-dependencies]
tauri-build = { version = "2.0", features = [] }

[dependencies]
tauri = { version = "2.0", features = ["macos-private-api"] }
tokio = { version = "1", features = ["full"] }
serde_json = "1.0"
serde = { version = "1.0", features = ["derive"] }
tauri-plugin-mic-recorder = "2.0.0"
rusqlite = { version = "0.29", features = ["bundled"] }
chrono = { version = "0.4", features = ["serde"] }

[features]
custom-protocol = ["tauri/custom-protocol"]
