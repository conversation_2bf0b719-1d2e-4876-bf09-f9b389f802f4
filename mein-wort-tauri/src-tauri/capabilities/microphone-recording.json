{"$schema": "../gen/schemas/capabilities.json", "identifier": "microphone-recording", "description": "Capability to allow microphone recording functionality", "context": "local", "windows": ["main"], "webviews": ["main"], "permissions": ["mic-recorder:default", "mic-recorder:allow-start-recording", "mic-recorder:allow-stop-recording", "mic-recorder:allow-get-recording"], "platforms": ["macOS", "windows", "linux"]}