// Native Tauri Audio Recording System mit Plugin
// Nur für Desktop - verwendet tauri-plugin-mic-recorder

class UnifiedAudioRecorder {
    constructor() {
        this.isRecording = false;
        this.startTime = 0;
        this.audioData = [];
        this.recordingType = 'unknown';
        this.recordingId = null;
        
        console.log('🎤 Native Tauri Audio Recorder with Plugin initializing...');
        this.detectAudioSystem();
    }

    detectAudioSystem() {
        console.log('🔍 Detecting Tauri audio system...');
        
        // Detailliertere Prüfung
        const hasTauriBase = !!window.__TAURI__;
        const hasTauriInvoke = !!(window.__TAURI__ && window.__TAURI__.invoke);
        const hasTauriCore = !!(window.__TAURI__ && window.__TAURI__.core);
        const hasTauriPlugin = !!(window.__TAURI__ && window.__TAURI__.core && window.__TAURI__.core.invoke);
        
        console.log('  - window.__TAURI__:', hasTauriBase);
        console.log('  - window.__TAURI__.invoke:', hasTauriInvoke);
        console.log('  - window.__TAURI__.core:', hasTauriCore);
        console.log('  - window.__TAURI__.core.invoke:', hasTauriPlugin);
        
        // Verwende die flexiblere Prüfung
        if (hasTauriPlugin || hasTauriInvoke) {
            this.recordingType = 'native';
            console.log('✅ Using NATIVE Tauri audio recording with plugin');
        } else if (hasTauriBase) {
            this.recordingType = 'native-loading';
            console.log('⏳ Tauri loading - will retry when ready');
        } else {
            this.recordingType = 'none';
            console.log('❌ Tauri not available - this is a desktop-only app');
        }
        
        console.log(`🎤 Final recording type: ${this.recordingType}`);
    }

    async startRecording() {
        console.log('🎤 Native startRecording() called, type:', this.recordingType);
        
        if (this.isRecording) {
            throw new Error('Aufnahme läuft bereits');
        }

        if (this.recordingType === 'none') {
            throw new Error('Desktop-App nicht bereit - bitte App neu starten');
        }
        
        if (this.recordingType === 'native-loading') {
            console.log('🔄 Tauri still loading, trying to re-detect...');
            this.detectAudioSystem();
            
            if (this.recordingType !== 'native') {
                throw new Error('Desktop-APIs noch nicht bereit - bitte kurz warten und nochmal versuchen');
            }
        }

        this.startTime = Date.now();

        try {
            return await this.startNativeRecording();
        } catch (error) {
            console.error('❌ Native recording failed:', error);
            throw new Error(`Desktop-Aufnahme fehlgeschlagen: ${error.message}`);
        }
    }

    async startNativeRecording() {
        try {
            console.log('🎤 Starting NATIVE Tauri recording with Plugin...');
            
            // Prüfe ob Tauri verfügbar ist - flexiblere Prüfung
            const tauriInvoke = window.__TAURI__?.invoke || window.__TAURI__?.core?.invoke;
            if (!tauriInvoke) {
                throw new Error('Tauri Desktop-APIs nicht verfügbar');
            }
            
            // Plugin-basierte Aufnahme starten
            console.log('🎤 Starting plugin-based recording...');
            
            // Generiere eindeutige Recording-ID
            this.recordingId = `recording_${Date.now()}`;
            
            // Starte Aufnahme mit Plugin - verwende die gefundene invoke Funktion
            const result = await tauriInvoke('plugin:mic-recorder|start_recording', {
                id: this.recordingId,
                config: {
                    sampleRate: 16000,
                    channels: 1,
                    format: 'wav'
                }
            });
            
            console.log('✅ Plugin recording started:', result);
            
            this.isRecording = true;
            return true;
        } catch (error) {
            console.error('❌ Plugin recording failed:', error);
            
            // Bessere Desktop-Fehlermeldungen
            if (error.toString().includes('Permission denied') || error.toString().includes('access denied')) {
                throw new Error('Mikrofon-Berechtigung verweigert - Bitte in Systemeinstellungen > Sicherheit > Mikrofon erlauben');
            } else if (error.toString().includes('No input device') || error.toString().includes('device not found')) {
                throw new Error('Kein Mikrofon gefunden - Bitte Mikrofon anschließen');
            } else if (error.toString().includes('device busy') || error.toString().includes('in use')) {
                throw new Error('Mikrofon wird von anderer App verwendet');
            } else {
                throw new Error(`Native Audio-Fehler: ${error}`);
            }
        }
    }

    async stopRecording() {
        console.log('🛑 Native stopRecording() called');
        
        if (!this.isRecording) {
            throw new Error('Keine Aufnahme aktiv');
        }

        try {
            console.log('🛑 Stopping plugin recording...');
            
            if (!this.recordingId) {
                throw new Error('Recording ID not found');
            }
            
            // Verwende die Tauri invoke Funktion
            const tauriInvoke = window.__TAURI__?.invoke || window.__TAURI__?.core?.invoke;
            
            // Stoppe Aufnahme mit Plugin - Dateipfad wird zurückgegeben
            const result = await tauriInvoke('plugin:mic-recorder|stop_recording', {
                id: this.recordingId
            });

            console.log('✅ Plugin recording stopped, result:', result);
            console.log('✅ Result type:', typeof result);
            console.log('✅ Result value:', JSON.stringify(result));

            this.isRecording = false;

            // Extrahiere den Dateipfad aus dem Result
            let filePath = result;
            if (typeof result === 'object' && result.filePath) {
                filePath = result.filePath;
            } else if (typeof result === 'object' && result.path) {
                filePath = result.path;
            } else if (typeof result === 'object' && result.file_path) {
                filePath = result.file_path;
            }

            console.log('✅ Extracted file path:', filePath);

            // Lade die Audio-Datei vom Pfad
            const audioBlob = await this.loadAudioFile(filePath);
            
            // Cleanup
            this.recordingId = null;
            
            return audioBlob;
        } catch (error) {
            console.error('❌ Plugin stop failed:', error);
            this.isRecording = false;
            this.recordingId = null;
            throw new Error(`Stop Recording fehlgeschlagen: ${error}`);
        }
    }

    getRecordingDuration() {
        if (!this.isRecording) return 0;
        return Date.now() - this.startTime;
    }

    isCurrentlyRecording() {
        return this.isRecording;
    }

    async loadAudioFile(filePath) {
        try {
            console.log('📁 Loading audio file from:', filePath);
            console.log('📁 FilePath type:', typeof filePath);
            console.log('📁 FilePath value:', JSON.stringify(filePath));

            // Prüfe ob filePath valid ist
            if (!filePath || typeof filePath !== 'string') {
                throw new Error(`Invalid file path: ${filePath}`);
            }

            // Verwende unser eigenes Tauri-Command
            const tauriInvoke = window.__TAURI__?.invoke || window.__TAURI__?.core?.invoke;

            // Debug: Zeige was wir senden
            const params = { filePath: filePath };
            console.log('📁 Sending params:', JSON.stringify(params));

            // Lade die Datei mit unserem eigenen Command - verwende camelCase für Tauri v2
            const fileBytes = await tauriInvoke('read_audio_file', params);

            console.log('✅ Audio file loaded, size:', fileBytes.length, 'bytes');

            // Konvertiere zu Blob
            const audioBlob = new Blob([new Uint8Array(fileBytes)], { type: 'audio/wav' });

            return audioBlob;
        } catch (error) {
            console.error('❌ Failed to load audio file:', error);
            throw new Error(`Fehler beim Laden der Audio-Datei: ${error.message}`);
        }
    }

    cleanup() {
        if (this.isRecording) {
            this.stopRecording();
        }
        console.log('✅ Native audio recorder cleaned up');
    }

    // Static methods
    static isSupported() {
        return !!(window.__TAURI__ && window.__TAURI__.invoke);
    }
}

// Export for use in other modules
window.UnifiedAudioRecorder = UnifiedAudioRecorder;