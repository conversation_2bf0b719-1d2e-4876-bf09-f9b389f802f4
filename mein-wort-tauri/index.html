<!DOCTYPE html>
<html lang="de">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MeinWort - Speech to Text Assistant</title>
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- Audio Recording and Whisper API modules -->
    <script src="src/unifiedAudioRecorder.js"></script>
    <script src="src/whisperAPI.js"></script>
    
    <!-- Three.js Import Map -->
    <script type="importmap">
        {
            "imports": {
                "three": "https://unpkg.com/three@0.158.0/build/three.module.js",
                "three/addons/": "https://unpkg.com/three@0.158.0/examples/jsm/"
            }
        }
    </script>
    
    <!-- Tauri v2 API Import -->
    <script type="module">
        console.log('🔄 Setting up Tauri v2 APIs...');
        
        // Import Tauri v2 API
        const { invoke } = await import('@tauri-apps/api/core');
        const { getCurrentWindow } = await import('@tauri-apps/api/window');
        const { writeText, readText } = await import('@tauri-apps/plugin-clipboard-manager');
        
        // Mache die APIs global verfügbar
        window.__TAURI_V2__ = {
            invoke: invoke,
            window: {
                getCurrentWindow: getCurrentWindow
            },
            clipboard: {
                writeText: writeText,
                readText: readText
            }
        };
        
        // Für Backwards-Kompatibilität
        window.__TAURI__ = window.__TAURI_V2__;
        
        console.log('✅ Tauri v2 APIs loaded');
        window.tauriReady = true;
        
        // Event für App-Komponenten
        window.dispatchEvent(new CustomEvent('tauriReady'));
    </script>
    <style>
        body {
            margin: 0;
            padding: 0;
            overflow: hidden;
            background: transparent;
        }
        
        #app {
            width: 200px;
            height: 200px;
            position: relative;
            background: rgba(25, 25, 25, 0.8);
            border-radius: 30px;
            overflow: visible;
            backdrop-filter: blur(20px) saturate(1.1);
            border: 1px solid rgba(255, 255, 255, 0.12);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.6);
            cursor: grab;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
        }

        #app.expanded {
            width: 600px;
            height: 400px;
            background: transparent;
            border: none;
            box-shadow: none;
            backdrop-filter: none;
        }

        /* Separater Visualizer-Container mit eigenem Background */
        .visualizer-container {
            position: absolute;
            top: 0;
            left: 0;
            width: 200px;
            height: 200px;
            background: rgba(25, 25, 25, 0.8);
            border-radius: 30px;
            backdrop-filter: blur(20px) saturate(1.1);
            border: 1px solid rgba(255, 255, 255, 0.12);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.6);
        }

        #app.expanded .visualizer-container {
            border-radius: 30px 0 30px 30px;
        }
        
        #app:active {
            cursor: grabbing;
        }
        
        .drag-area:active {
            cursor: grabbing;
        }

        .button-container {
            position: absolute;
            top: 125px;
            left: 0;
            width: 200px;
            display: flex;
            justify-content: space-evenly;
            align-items: center;
            padding: 0 20px;
            z-index: 1000;
            -webkit-app-region: no-drag;
        }


        .main-button {
            width: 50px;
            height: 50px;
            border-radius: 15px;
            border: none;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 18px;
            background: rgba(60, 60, 60, 0.4);
            color: rgba(255, 255, 255, 0.9);
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.5), 
                        inset 0 1px 1px rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(20px) saturate(1.2);
            border: 1px solid rgba(255, 255, 255, 0.15);
            -webkit-app-region: no-drag;
        }

        .side-button {
            width: 40px;
            height: 40px;
            border-radius: 12px;
            border: none;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 16px;
            background: rgba(60, 60, 60, 0.4);
            color: rgba(255, 255, 255, 0.9);
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.5), 
                        inset 0 1px 1px rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(20px) saturate(1.2);
            border: 1px solid rgba(255, 255, 255, 0.15);
            -webkit-app-region: no-drag;
        }

        .ai-button {
            background: linear-gradient(135deg, rgba(255, 107, 53, 0.6), rgba(247, 147, 30, 0.6));
            color: white;
        }

        .ai-button.recording {
            background: linear-gradient(135deg, #ff4757, #ff3838);
            animation: pulse 1.5s infinite;
        }

        .side-button:hover {
            transform: scale(1.05);
            background: rgba(80, 80, 80, 0.6);
            box-shadow: 0 6px 25px rgba(0, 0, 0, 0.6), 
                        inset 0 1px 1px rgba(255, 255, 255, 0.2);
        }

        .ai-button:hover {
            transform: scale(1.05);
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }

        #visualizer {
            position: absolute;
            top: 0;
            left: 0;
            width: 200px;
            height: 200px;
            border-radius: 30px;
            overflow: hidden;
        }
        
        #visualizer canvas {
            border-radius: 30px;
        }

        .button-label {
            position: absolute;
            bottom: -18px;
            left: 50%;
            transform: translateX(-50%);
            color: rgba(255, 255, 255, 0.95);
            font-size: 9px;
            font-weight: 700;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.8);
            white-space: nowrap;
            backdrop-filter: blur(5px);
        }

        /* Expandable Panel Styles */
        .expandable-panel {
            position: absolute;
            top: 0;
            left: 200px;
            width: 400px;
            height: 400px;
            background: rgba(25, 25, 25, 0.95);
            border-radius: 0 30px 30px 30px;
            backdrop-filter: blur(20px) saturate(1.1);
            border: 1px solid rgba(255, 255, 255, 0.12);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.6);
            overflow: hidden;
            opacity: 0;
            transform: translateX(-20px);
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            pointer-events: none;
        }

        .expandable-panel.active {
            opacity: 1;
            transform: translateX(0);
            pointer-events: all;
        }

        .panel-header {
            padding: 20px 20px 15px 20px;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .panel-title {
            color: rgba(255, 255, 255, 0.9);
            font-size: 14px;
            font-weight: 700;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .panel-close {
            width: 24px;
            height: 24px;
            border-radius: 6px;
            background: rgba(60, 60, 60, 0.6);
            border: none;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            color: rgba(255, 255, 255, 0.8);
            font-size: 12px;
            transition: all 0.3s ease;
        }

        .panel-close:hover {
            background: rgba(80, 80, 80, 0.8);
            transform: scale(1.05);
        }

        .panel-content {
            padding: 20px;
            height: calc(100% - 75px);
            overflow-y: auto;
        }

        .panel-section {
            background: rgba(255, 255, 255, 0.05);
            border-radius: 12px;
            padding: 15px;
            margin-bottom: 15px;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        .panel-section-title {
            color: rgba(255, 255, 255, 0.9);
            font-size: 12px;
            font-weight: 700;
            margin-bottom: 10px;
            text-transform: uppercase;
            letter-spacing: 0.3px;
        }

        .panel-button {
            width: 100%;
            padding: 10px 15px;
            background: rgba(60, 60, 60, 0.6);
            color: rgba(255, 255, 255, 0.9);
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 11px;
            font-weight: 600;
            margin-bottom: 8px;
            transition: all 0.3s ease;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        .panel-button:hover {
            background: rgba(80, 80, 80, 0.8);
            border-color: rgba(255, 255, 255, 0.2);
            transform: translateY(-1px);
        }

        .panel-button.primary {
            background: linear-gradient(135deg, rgba(255, 107, 53, 0.7), rgba(247, 147, 30, 0.7));
        }

        .panel-button.primary:hover {
            background: linear-gradient(135deg, rgba(255, 107, 53, 0.9), rgba(247, 147, 30, 0.9));
        }

        .panel-button:disabled {
            background: rgba(60, 60, 60, 0.3);
            color: rgba(255, 255, 255, 0.4);
            cursor: not-allowed;
            border-color: rgba(255, 255, 255, 0.05);
        }

        .panel-button:disabled:hover {
            background: rgba(60, 60, 60, 0.3);
            transform: none;
            border-color: rgba(255, 255, 255, 0.05);
        }
    </style>
</head>
<body>
    <div id="app">
        
        <!-- Visualizer Container mit eigenem Background -->
        <div class="visualizer-container">
            <!-- Canvas Partikel Visualizer -->
            <div id="visualizer"></div>
        </div>
        
        <!-- Haupt-Button-Container -->
        <div class="button-container">
            <!-- Document Button -->
            <div style="position: relative;">
                <button id="documentBtn" class="side-button">
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M14,2H6A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2M18,20H6V4H13V9H18V20Z"/>
                    </svg>
                    <div class="button-label">Document</div>
                </button>
            </div>

            <!-- AI+ Button (Hauptfunktion) -->
            <div style="position: relative;">
                <button id="aiBtn" class="main-button ai-button">
                    <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M12,17.27L18.18,21L16.54,13.97L22,9.24L14.81,8.62L12,2L9.19,8.62L2,9.24L7.46,13.97L5.82,21L12,17.27Z"/>
                    </svg>
                    <div class="button-label">AI+</div>
                </button>
            </div>

            <!-- Select+ Button -->
            <div style="position: relative;">
                <button id="selectBtn" class="side-button">
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M13,1L11,3L13,5L15,3M19,8L17,6L19,4L21,6M20,12.5V11.5L23,12.5L20,13.5M15,19L13,21L11,19L13,17M6,8L4,6L2,8L4,10M1,12.5L4,11.5V13.5M8,5L10,3L8,1L6,3M8,19L6,21L8,23L10,21"/>
                    </svg>
                    <div class="button-label">Select+</div>
                </button>
            </div>
        </div>

        <!-- Expandable Panel -->
        <div id="expandablePanel" class="expandable-panel">
            <div class="panel-header">
                <div class="panel-title" id="panelTitle">📄 Document</div>
                <button class="panel-close" onclick="closeExpandablePanel()">✕</button>
            </div>
            <div class="panel-content" id="panelContent">
                <!-- Content wird dynamisch geladen -->
            </div>
        </div>
    </div>

    <script type="module">
        // Three.js Imports
        import * as THREE from 'three';
        
        console.log('🚀 MeinWort - Starting Three.js Organic Swarm Visualizer...');
        
        // Organischer Schwarm Three.js Visualizer (VU3 Original für MeinWort 200x200px)
        class OrganicSwarmVisualizer {
            constructor() {
                console.log('🎨 OrganicSwarmVisualizer initializing...');
                
                // VU3 Original Einstellungen - skaliert für 200x200px MeinWort Format
                this.settings = {
                    // Schwarm-Basis (VU3 Original, optimiert für 200x200px)
                    particleCount: 5000,        // Mittlere Partikel-Dichte
                    particleSize: 1.5,          // VU3 Original
                    startRadius: 90,            // Skaliert von 300 für 200x200px
                    particleLifetime: 8,        // VU3 Original

                    // Bewegungs-Kräfte (VU3 Original)
                    centerAttraction: 2,        // VU3 Original
                    noiseStrength: 5,           // VU3 Original
                    noiseSpeed: 1,              // VU3 Original
                    inertia: 0.95,              // VU3 Original

                    // Schwarm-Verhalten (VU3 Original Flocking)
                    cohesion: 1,                // VU3 Original - Zusammenhalt
                    separation: 2,              // VU3 Original - Abstand halten
                    alignment: 1,               // VU3 Original - Ausrichtung zu Nachbarn
                    neighborRadius: 50,         // VU3 Original - Nachbarschafts-Radius

                    // Rotation (deaktiviert für MeinWort)
                    rotationEnabled: false,     // Keine Rotation für cleanen Look
                    rotationSpeed: 0.5,         // VU3 Original
                    rotationX: 0,               // VU3 Original
                    rotationY: 0,               // VU3 Original
                    rotationZ: 0,               // VU3 Original
                    randomRotation: false,      // VU3 Original

                    // Farbsystem (VU3 Original für Audio-Reaktivität)
                    colorMode: 'monochrome',    // Weiße Partikel
                    hue: 0,                     // Irrelevant für monochrome
                    saturation: 0,              // 0 = weiß
                    brightness: 100,            // VU3 Original
                    colorSpeed: 1,              // VU3 Original

                    // Erweiterte Effekte (VU3 Original)
                    turbulence: 0,              // VU3 Original
                    turbulenceSpeed: 1,         // VU3 Original
                    gravity: 0,                 // VU3 Original
                    magneticField: 0,           // VU3 Original
                    vortexStrength: 0,          // VU3 Original
                    damping: 0.99,              // VU3 Original
                    sizeVariation: 1,           // VU3 Original
                    opacity: 0.8,               // VU3 Original

                    // Partikel-Spuren (VU3 Original)
                    trails: false,              // VU3 Original
                    trailLength: 10,            // VU3 Original
                    trailOpacity: 0.5,          // VU3 Original

                    // Partikel-Emission (VU3 Original)
                    emissionEnabled: false,     // VU3 Original
                    emissionRate: 100,          // VU3 Original
                    emissionSpeed: 50,          // VU3 Original
                    emissionSpread: 45,         // VU3 Original

                    // Bewegungs-Effekte (VU3 Original)
                    pulseEnabled: true,         // VU3 Original
                    pulseSpeed: 3,              // VU3 Original
                    waveEnabled: true,          // VU3 Original
                    waveSpeed: 2,               // VU3 Original
                    waveHeight: 25,             // Reduziert von 50 für 200x200px Format
                    chaosLevel: 0,              // VU3 Original

                    // Audio-Reaktivität (VU3 Original)
                    audioEnabled: true,         // VU3 Original
                    bassImpact: 15,             // VU3 Original
                    midImpact: 12,              // VU3 Original
                    highImpact: 8,              // VU3 Original
                    bassToNoise: 15,            // VU3 Original
                    midToSpeed: 8,              // VU3 Original
                    highToRotation: 3,          // VU3 Original
                    speechSensitivity: 3        // VU3 Original
                };

                // System-Variablen
                this.scene = null;
                this.camera = null;
                this.renderer = null;
                this.particleSystem = null;
                this.particles = null;
                this.velocities = null;
                this.lifetimes = null;
                this.startPositions = null;

                // Audio-System
                this.audioContext = null;
                this.analyser = null;
                this.dataArray = null;
                this.audioData = { low: 0, mid: 0, high: 0, volume: 0 };
                this.isAudioActive = false;

                // Animation
                this.time = 0;
                this.rotationAngle = 0;
                this.frameCount = 0;
                this.lastFpsTime = 0;
                this.fps = 0;
                this.isRecording = false;

                this.init();
            }

            // VU3 Original: Komplettes Farbsystem
            getParticleColor(index, distance, audioData) {
                const time = this.time * this.settings.colorSpeed;
                const normalizedDistance = Math.min(distance / 500, 1);
                
                let hue = this.settings.hue;
                let saturation = this.settings.saturation;
                let brightness = this.settings.brightness;
                
                switch (this.settings.colorMode) {
                    case 'audio':
                        // VU3 Original: Audio-reaktive Farben
                        hue = (this.settings.hue + audioData.volume * 60) % 360;
                        brightness = Math.min(this.settings.brightness + audioData.volume * 30, 100);
                        saturation = Math.min(this.settings.saturation + audioData.high * 20, 100);
                        break;
                        
                    case 'spectrum':
                        // VU3 Original: Spektrum basierend auf Position
                        hue = (normalizedDistance * 360 + time * 30) % 360;
                        break;
                        
                    case 'rainbow':
                        // VU3 Original: Rotierender Regenbogen
                        hue = (index * 2 + time * 50) % 360;
                        break;
                        
                    case 'gradient':
                        // VU3 Original: Verlauf von innen nach außen
                        hue = this.settings.hue + normalizedDistance * 60;
                        brightness = this.settings.brightness * (1 - normalizedDistance * 0.3);
                        break;
                        
                    case 'fire':
                        // VU3 Original: Feuer-Effekt
                        hue = 20 + Math.sin(time + index * 0.1) * 15;
                        saturation = 90 + Math.sin(time * 2 + index * 0.05) * 10;
                        brightness = 80 + Math.sin(time * 3 + index * 0.02) * 20;
                        break;
                        
                    case 'ice':
                        // VU3 Original: Eis-Effekt
                        hue = 200 + Math.sin(time + index * 0.1) * 30;
                        saturation = 70 + Math.sin(time * 1.5 + index * 0.03) * 20;
                        brightness = 85 + Math.sin(time * 2 + index * 0.04) * 15;
                        break;
                        
                    case 'neon':
                        // VU3 Original: Neon-Effekt
                        const neonColors = [300, 60, 120, 180, 240]; // Magenta, Gelb, Grün, Cyan, Blau
                        hue = neonColors[Math.floor((index + time * 10) % neonColors.length)];
                        saturation = 100;
                        brightness = 90 + Math.sin(time * 5 + index * 0.1) * 10;
                        break;
                        
                    case 'monochrome':
                        // VU3 Original: Einfarbig
                        saturation = 0;
                        brightness = this.settings.brightness * (1 - normalizedDistance * 0.2);
                        break;
                }
                
                // VU3 Original: HSV zu RGB konvertieren
                return this.hsvToRgb(hue, saturation / 100, brightness / 100);
            }

            // HSV zu RGB Konvertierung
            hsvToRgb(h, s, v) {
                h = h / 360;
                const c = v * s;
                const x = c * (1 - Math.abs((h * 6) % 2 - 1));
                const m = v - c;

                let r, g, b;

                if (h < 1/6) {
                    r = c; g = x; b = 0;
                } else if (h < 2/6) {
                    r = x; g = c; b = 0;
                } else if (h < 3/6) {
                    r = 0; g = c; b = x;
                } else if (h < 4/6) {
                    r = 0; g = x; b = c;
                } else if (h < 5/6) {
                    r = x; g = 0; b = c;
                } else {
                    r = c; g = 0; b = x;
                }

                return {
                    r: r + m,
                    g: g + m,
                    b: b + m
                };
            }

            // Initialisierung mit IDLE-Modus
            init() {
                this.setupScene();
                this.createParticleSystem();
                
                // Starte im IDLE-Modus (wie Screenshot)
                this.switchToIdleMode();
                
                this.animate();
                console.log('✅ Organic Swarm Visualizer initialized in IDLE mode');
            }

            setupScene() {
                const container = document.getElementById('visualizer');
                if (!container) {
                    console.error('❌ Visualizer container not found!');
                    return;
                }

                // Scene
                this.scene = new THREE.Scene();
                this.scene.background = null; // Transparenter Hintergrund

                // Camera - angepasst für 200x200px
                this.camera = new THREE.PerspectiveCamera(75, container.clientWidth / container.clientHeight, 1, 1000);
                this.camera.position.set(0, 0, 150);

                // Renderer
                this.renderer = new THREE.WebGLRenderer({ antialias: true, alpha: true });
                this.renderer.setSize(container.clientWidth, container.clientHeight);
                this.renderer.setPixelRatio(Math.min(window.devicePixelRatio, 2));
                this.renderer.setClearColor(0x000000, 0.0); // Vollständig transparent
                this.renderer.domElement.style.borderRadius = '30px';
                container.appendChild(this.renderer.domElement);

                // Resize Handler
                window.addEventListener('resize', () => this.onWindowResize());
                console.log('✅ Three.js scene setup complete');
            }

            createParticleSystem() {
                const count = this.settings.particleCount;

                // Geometrie
                const geometry = new THREE.BufferGeometry();
                const positions = new Float32Array(count * 3);
                const colors = new Float32Array(count * 3);

                // Arrays für Physik
                this.velocities = new Float32Array(count * 3);
                this.lifetimes = new Float32Array(count);
                this.startPositions = new Float32Array(count * 3);

                // Partikel auf Startkreis initialisieren
                for (let i = 0; i < count; i++) {
                    this.initializeParticle(i, positions, colors);
                }

                geometry.setAttribute('position', new THREE.BufferAttribute(positions, 3));
                geometry.setAttribute('color', new THREE.BufferAttribute(colors, 3));

                // Material
                const material = new THREE.PointsMaterial({
                    size: this.settings.particleSize,
                    vertexColors: true,
                    transparent: true,
                    opacity: 0.8,
                    blending: THREE.AdditiveBlending,
                    sizeAttenuation: true
                });

                // Partikel-System
                this.particleSystem = new THREE.Points(geometry, material);
                this.scene.add(this.particleSystem);

                this.particles = positions;
                console.log('✅ Three.js particle system created:', count);
            }

            // VU3 ORIGINAL: Partikel spawnen auf Kreis-Rand und fließen durch Physik zum Zentrum
            initializeParticle(index, positions, colors) {
                const i3 = index * 3;
                
                // Variable Spawn-Radius: 0-90px statt fester Ring
                const angle = Math.random() * Math.PI * 2;
                const radius = Math.random() * 90; // Variable Radius 0-90px
                
                const x = Math.cos(angle) * radius;
                const y = Math.sin(angle) * radius;
                const z = (Math.random() - 0.5) * 20; // Leichte Z-Variation
                
                // Position setzen
                positions[i3] = x;
                positions[i3 + 1] = y;
                positions[i3 + 2] = z;

                // Startposition merken (VU3 Original)
                this.startPositions[i3] = x;
                this.startPositions[i3 + 1] = y;
                this.startPositions[i3 + 2] = z;

                // Geschwindigkeit zurücksetzen (VU3 Original)
                this.velocities[i3] = 0;
                this.velocities[i3 + 1] = 0;
                this.velocities[i3 + 2] = 0;

                // Lebensdauer setzen (VU3 Original)
                this.lifetimes[index] = Math.random() * this.settings.particleLifetime;

                // Farbe (VU3 Original Farbsystem mit Cyan)
                const distance = Math.sqrt(x*x + y*y);
                const color = this.getParticleColor(index, distance, this.audioData);
                colors[i3] = color.r;
                colors[i3 + 1] = color.g;
                colors[i3 + 2] = color.b;
            }

            // Noise-Funktion für organische Bewegungen
            noise3D(x, y, z, time) {
                const scale = 0.01;
                const timeScale = this.settings.noiseSpeed * 0.1;

                const nx = x * scale + time * timeScale;
                const ny = y * scale + time * timeScale * 0.7;
                const nz = z * scale + time * timeScale * 0.5;

                return Math.sin(nx * 2.1) * Math.cos(ny * 1.7) * Math.sin(nz * 2.3) +
                       Math.sin(nx * 1.3) * Math.cos(ny * 2.9) * Math.sin(nz * 1.1) * 0.5 +
                       Math.sin(nx * 3.7) * Math.cos(ny * 1.1) * Math.sin(nz * 2.7) * 0.25;
            }

            // VU3 Original: Partikel-Update mit exakter VU3-Physik
            updateParticles(deltaTime) {
                const positions = this.particleSystem.geometry.attributes.position.array;
                const colors = this.particleSystem.geometry.attributes.color.array;
                const count = this.settings.particleCount;

                // VU3 Original: Audio-Reaktivität
                const audioNoiseBoost = this.isAudioActive ? this.audioData.low * this.settings.bassToNoise : 0;
                const audioSpeedBoost = this.isAudioActive ? this.audioData.mid * this.settings.midToSpeed : 0;
                const audioRotationBoost = this.isAudioActive ? this.audioData.high * this.settings.highToRotation : 0;

                // VU3 Original: Aktuelle Noise-Stärke
                const currentNoiseStrength = this.settings.noiseStrength + audioNoiseBoost;
                const currentNoiseSpeed = this.settings.noiseSpeed + audioSpeedBoost * 0.1;

                for (let i = 0; i < count; i++) {
                    const i3 = i * 3;

                    // Aktuelle Position
                    let x = positions[i3];
                    let y = positions[i3 + 1];
                    let z = positions[i3 + 2];

                    // VU3 Original: Lebensdauer verringern
                    this.lifetimes[i] -= deltaTime;

                    // VU3 ORIGINAL: Partikel respawnen auf Kreis-Rand
                    if (this.lifetimes[i] <= 0) {
                        // Zurück zur Startposition (VU3 Original)
                        x = this.startPositions[i3];
                        y = this.startPositions[i3 + 1];
                        z = this.startPositions[i3 + 2];

                        // Geschwindigkeit zurücksetzen (VU3 Original)
                        this.velocities[i3] = 0;
                        this.velocities[i3 + 1] = 0;
                        this.velocities[i3 + 2] = 0;

                        // Neue Lebensdauer (VU3 Original)
                        this.lifetimes[i] = this.settings.particleLifetime;

                        // Neue Startposition variabel (0-90px)
                        const angle = Math.random() * Math.PI * 2;
                        const radius = Math.random() * 90; // Variable Radius 0-90px
                        this.startPositions[i3] = Math.cos(angle) * radius;
                        this.startPositions[i3 + 1] = Math.sin(angle) * radius;
                        this.startPositions[i3 + 2] = (Math.random() - 0.5) * 20;

                        x = this.startPositions[i3];
                        y = this.startPositions[i3 + 1];
                        z = this.startPositions[i3 + 2];
                    }

                    // VU3 Original: Zentrum-Sog (Kraft zum Mittelpunkt) - normale Werte
                    const centerForceX = -x * this.settings.centerAttraction * 0.002;
                    const centerForceY = -y * this.settings.centerAttraction * 0.002;
                    const centerForceZ = -z * this.settings.centerAttraction * 0.002;

                    // VU3 Original: Noise-Wind (chaotische Kraft) - normale Werte
                    const noiseX = this.noise3D(x, y, z, this.time) * currentNoiseStrength * 0.2;
                    const noiseY = this.noise3D(x + 100, y, z, this.time) * currentNoiseStrength * 0.2;
                    const noiseZ = this.noise3D(x, y + 100, z, this.time) * currentNoiseStrength * 0.2;

                    // VU3 ORIGINAL: Gesamtkraft (exakt wie VU3)
                    const forceX = centerForceX + noiseX;
                    const forceY = centerForceY + noiseY;
                    const forceZ = centerForceZ + noiseZ;

                    // VU3 Original: Geschwindigkeit aktualisieren (mit Trägheit)
                    this.velocities[i3] = this.velocities[i3] * this.settings.inertia + forceX;
                    this.velocities[i3 + 1] = this.velocities[i3 + 1] * this.settings.inertia + forceY;
                    this.velocities[i3 + 2] = this.velocities[i3 + 2] * this.settings.inertia + forceZ;

                    // Position aktualisieren
                    x += this.velocities[i3];
                    y += this.velocities[i3 + 1];
                    z += this.velocities[i3 + 2];

                    // Position setzen
                    positions[i3] = x;
                    positions[i3 + 1] = y;
                    positions[i3 + 2] = z;

                    // VU3 Original: Pulsation
                    if (this.settings.pulseEnabled) {
                        const pulseEffect = Math.sin(this.time * this.settings.pulseSpeed) * 0.1;
                        const audioPulse = this.isAudioActive ? this.audioData.low * this.settings.bassImpact * 0.01 : 0;
                        const totalPulse = pulseEffect + audioPulse;
                        
                        this.velocities[i3] += (x / 100) * totalPulse;
                        this.velocities[i3 + 1] += (y / 100) * totalPulse;
                        this.velocities[i3 + 2] += (z / 100) * totalPulse;
                    }

                    // VU3 Original: Wellen-Effekt
                    if (this.settings.waveEnabled) {
                        const waveX = Math.sin(this.time * this.settings.waveSpeed + x * 0.01) * this.settings.waveHeight * 0.001;
                        const waveY = Math.sin(this.time * this.settings.waveSpeed + y * 0.01) * this.settings.waveHeight * 0.001;
                        const audioWave = this.isAudioActive ? this.audioData.mid * this.settings.midImpact * 0.001 : 0;
                        
                        this.velocities[i3] += waveX + audioWave;
                        this.velocities[i3 + 1] += waveY + audioWave;
                    }

                    // VU3 Original: Dämpfung anwenden
                    this.velocities[i3] *= this.settings.damping;
                    this.velocities[i3 + 1] *= this.settings.damping;
                    this.velocities[i3 + 2] *= this.settings.damping;

                    // VU3 Original: Farbe mit erweitertem Farbsystem
                    const particleDistance = Math.sqrt(x * x + y * y + z * z);
                    const color = this.getParticleColor(i, particleDistance, this.audioData);

                    // Größen-Variation
                    let sizeMultiplier = 1;
                    if (this.settings.sizeVariation > 0) {
                        sizeMultiplier = 1 + Math.sin(this.time * 2 + i * 0.1) * this.settings.sizeVariation * 0.2;
                        // Audio-reaktive Größe
                        if (this.isAudioActive) {
                            sizeMultiplier += this.audioData.high * this.settings.highImpact * 0.01;
                        }
                    }

                    colors[i3] = color.r * this.settings.opacity;
                    colors[i3 + 1] = color.g * this.settings.opacity;
                    colors[i3 + 2] = color.b * this.settings.opacity;
                }

                // Rotation des gesamten Schwarms (disabled für MeinWort)
                const rotationEnabled = this.settings.rotationEnabled !== false; // Default: false
                if (rotationEnabled) {
                    const baseRotationSpeed = this.settings.rotationSpeed || 0.2;
                    const rotationSpeed = baseRotationSpeed + audioRotationBoost * 0.1;
                    this.rotationAngle += rotationSpeed * deltaTime;
                    this.particleSystem.rotation.z = this.rotationAngle;
                }

                // Geometrie aktualisieren
                this.particleSystem.geometry.attributes.position.needsUpdate = true;
                this.particleSystem.geometry.attributes.color.needsUpdate = true;

                // Partikel-Größe aktualisieren
                this.particleSystem.material.size = this.settings.particleSize;
            }

            onWindowResize() {
                const container = document.getElementById('visualizer');
                if (container) {
                    this.camera.aspect = container.clientWidth / container.clientHeight;
                    this.camera.updateProjectionMatrix();
                    this.renderer.setSize(container.clientWidth, container.clientHeight);
                }
            }

            setRecordingState(recording) {
                this.isRecording = recording;
                console.log('🎤 Three.js Organic Swarm - Recording state:', recording);
                
                // Partikel-Modus umschalten
                if (recording) {
                    this.switchToVoiceActiveMode();
                } else {
                    this.switchToIdleMode();
                }
            }

            // Erweiterte Audio-Daten-Verarbeitung für Spracherkennung
            updateAudioData(audioData) {
                if (!audioData) return;
                
                // Basis Audio-Daten normalisiert
                const rawVolume = Math.min(audioData.volume || 0, 1);
                const rawLow = Math.min(audioData.low || 0, 1);
                const rawMid = Math.min(audioData.mid || 0, 1);
                const rawHigh = Math.min(audioData.high || 0, 1);
                
                // Sprach-optimierte Verarbeitung
                const speechSensitivity = this.settings.speechSensitivity || 3.5;
                
                // Mitten-Frequenzen verstärken (Sprache liegt hauptsächlich in 300-3000 Hz)
                const speechEnhancedMid = rawMid * (1 + speechSensitivity * 0.2);
                
                // Sprach-Charakteristika erkennen
                const speechIndicator = this.detectSpeechPattern(rawLow, rawMid, rawHigh);
                
                // Audio-Daten für Partikel-System mit Sprach-Optimierung
                this.audioData = {
                    volume: rawVolume,
                    low: rawLow,
                    mid: Math.min(speechEnhancedMid, 1),
                    high: rawHigh,
                    speech: speechIndicator,
                    speechIntensity: speechIndicator * speechSensitivity * 0.3
                };
                
                // Sprach-spezifische Verstärkung
                if (speechIndicator > 0.3) {
                    this.audioData.speechBoost = speechIndicator * speechSensitivity * 0.5;
                } else {
                    this.audioData.speechBoost = 0;
                }
                
                // Audio-Aktivität setzen
                this.isAudioActive = rawVolume > 0.01;
            }

            // Sprach-Muster-Erkennung für bessere Audio-Reaktivität
            detectSpeechPattern(low, mid, high) {
                // Sprache hat typischerweise:
                // - Moderate Bass-Anteile (Grundfrequenz der Stimme)
                // - Starke Mitten (Vokale und Konsonanten)
                // - Variable Höhen (Zischlaute, etc.)
                
                const speechCharacteristics = {
                    midDominance: mid > (low + high) * 0.6, // Mitten dominieren
                    balancedSpectrum: (mid / (low + high + 0.1)) > 1.2, // Ausgewogenes Spektrum
                    moderateBass: low > 0.1 && low < 0.7, // Nicht zu viel/wenig Bass
                    dynamicRange: Math.abs(mid - low) > 0.15 // Dynamische Variation
                };
                
                // Sprach-Wahrscheinlichkeit berechnen
                let speechProbability = 0;
                
                if (speechCharacteristics.midDominance) speechProbability += 0.4;
                if (speechCharacteristics.balancedSpectrum) speechProbability += 0.3;
                if (speechCharacteristics.moderateBass) speechProbability += 0.2;
                if (speechCharacteristics.dynamicRange) speechProbability += 0.1;
                
                // Smoothing für natürlichere Übergänge
                this.speechSmoothingBuffer = this.speechSmoothingBuffer || [];
                this.speechSmoothingBuffer.push(speechProbability);
                if (this.speechSmoothingBuffer.length > 5) {
                    this.speechSmoothingBuffer.shift();
                }
                
                const smoothedSpeech = this.speechSmoothingBuffer.reduce((a, b) => a + b, 0) / this.speechSmoothingBuffer.length;
                return Math.min(smoothedSpeech, 1);
            }

            switchToIdleMode() {
                console.log('😴 Switching to IDLE mode');
                // Partikel-Einstellungen für organischen ruhigen Zustand
                this.settings.particleCount = 6000;  // Weniger Partikel für bessere Performance
                this.settings.centerAttraction = 1.5; // Moderater Zentrum-Sog
                this.settings.noiseStrength = 3;    // Moderate organische Bewegung
                this.settings.turbulence = 0;       // Keine Turbulenz
                this.settings.opacity = 0.7;        // Etwas weniger intensiv
                this.settings.inertia = 0.96;       // Mehr Trägheit = ruhigere Bewegung
            }

            switchToVoiceActiveMode() {
                console.log('🗣️ Switching to VOICE-ACTIVE mode');
                // Partikel-Einstellungen für aktiven Zustand
                this.settings.particleCount = 10000; // Mehr Partikel
                this.settings.centerAttraction = 2.5; // Stärkerer Zentrum-Sog
                this.settings.noiseStrength = 6;     // Mehr organische Bewegung
                this.settings.turbulence = 1;        // Leichte Turbulenz
                this.settings.opacity = 0.9;         // Intensiver
                this.settings.inertia = 0.94;        // Weniger Trägheit = dynamischere Bewegung
            }

            // Haupt-Animations-Loop
            animate() {
                requestAnimationFrame(() => this.animate());

                const deltaTime = 0.016; // ~60fps
                this.time += deltaTime;
                this.frameCount++;

                // Partikel aktualisieren
                this.updateParticles(deltaTime);

                // Rendern
                this.renderer.render(this.scene, this.camera);
            }
        }
        
        // App Hauptklasse
        class MeinWortApp {
            constructor() {
                console.log('🎯 MeinWortApp initializing...');
                // State Management System
                this.appState = {
                    // Recording State Machine
                    recordingState: 'IDLE', // IDLE, RECORDING, PROCESSING, TRANSCRIBING, COMPLETED, ERROR
                    recordingStartTime: null,
                    recordingDuration: 0,

                    // Audio System
                    audioRecorder: null,
                    audioRecorderType: 'none', // 'web', 'native', 'unified', 'none'
                    audioDevice: null,

                    // API & Transcription
                    whisperAPI: null,
                    currentTranscription: null,

                    // UI State
                    activePanel: null,
                    lastError: null,

                    // Data
                    recordings: [],
                    costStats: null,

                    // Settings State
                    settingsChanged: false,
                    settingsValid: true,
                    settingsErrors: {},

                    // Audio Devices
                    availableAudioDevices: [],
                    currentAudioDevice: null,
                    audioDeviceTestInProgress: false,

                    // AI Processing
                    aiProcessingInProgress: false,
                    lastEnhancement: null,
                    enhancementHistory: [],

                    // Workflow Automation
                    availableWorkflows: [],
                    activeWorkflows: [],
                    workflowExecutionInProgress: false,
                    lastWorkflowExecution: null,

                    // Audio Processing
                    audioProcessingEnabled: false,
                    audioProcessingInProgress: false,
                    audioProcessingPresets: [],
                    lastAudioProcessingResult: null
                };

                // Legacy properties (für Kompatibilität)
                this.isRecording = false;
                this.isDragging = false;
                this.visualizer = null;
                this.audioRecorder = null;
                this.whisperAPI = null;
                this.audioAnalyzer = null;
                this.isPanelExpanded = false;
                this.recordings = [];
                this.currentTranscription = null;
                // Erweiterte Settings mit Validierung
                this.defaultSettings = {
                    // API & Transcription
                    openaiApiKey: null,
                    language: 'de',

                    // Recording
                    audioQuality: 'high', // 'low', 'medium', 'high'
                    maxRecordingDuration: 300, // Sekunden
                    autoStopRecording: false,

                    // UI & Behavior
                    hotkeyEnabled: true,
                    autoClipboard: true,
                    showNotifications: true,
                    autoSaveRecordings: true,

                    // Advanced
                    debugMode: false,
                    costWarningThreshold: 1.0, // Dollar pro Tag

                    // AI Features
                    enableAIEnhancement: true,
                    autoGenerateSummary: true,
                    autoExtractKeywords: true,
                    autoDetectActionItems: true,

                    // Workflow Automation
                    enableWorkflowAutomation: true,
                    autoExecuteWorkflows: true,

                    // Audio Processing
                    enableAudioProcessing: true,
                    audioProcessingPreset: 'standard', // 'off', 'light', 'standard', 'aggressive'
                    customAudioSettings: {
                        noiseReduction: 0.7,
                        volumeNormalization: true,
                        voiceEnhancement: 0.5,
                        highPassFilter: true
                    },

                    // System
                    microphonePermissionGranted: false,
                    microphonePermissionAsked: false,
                    preferredAudioDevice: null
                };

                this.settings = { ...this.defaultSettings };
                
                // Einstellungen aus localStorage laden
                this.loadSettings();
                
                // Three.js Organic Swarm Visualizer initialisieren
                this.visualizer = new OrganicSwarmVisualizer();
                
                // Audio-System initialisieren wenn Tauri bereit ist
                this.setupTauriListener();

                // Einstellungen laden
                setTimeout(() => this.loadAppSettings(), 200);

                // UI Setup - warten bis alles geladen ist
                setTimeout(() => this.setupUI(), 500);

                // Dragging Setup - mehrfach versuchen
                setTimeout(() => this.setupDragging(), 1000);
                setTimeout(() => this.setupDragging(), 2000);
            }

            async initAudioSystem() {
                console.log('🎤 Initializing audio system...');
                try {
                    // Web Audio API für Real-time Analysis
                    this.audioContext = new (window.AudioContext || window.webkitAudioContext)();
                    this.audioAnalyzer = this.audioContext.createAnalyser();
                    this.audioAnalyzer.fftSize = 256;
                    this.audioData = new Uint8Array(this.audioAnalyzer.frequencyBinCount);
                    
                    // Unified AudioRecorder initialisieren - einfach und robust
                    if (typeof UnifiedAudioRecorder !== 'undefined') {
                        // Konfiguration mit bevorzugtem Audio-Gerät
                        const audioConfig = {
                            preferredDeviceId: this.settings.preferredAudioDevice,
                            quality: this.settings.audioQuality || 'high'
                        };

                        this.audioRecorder = new UnifiedAudioRecorder(audioConfig);
                        this.audioRecorderType = this.audioRecorder.recordingType;

                        // Audio-Gerät in State speichern
                        this.setState({
                            audioRecorder: this.audioRecorder,
                            audioRecorderType: this.audioRecorderType,
                            currentAudioDevice: this.settings.preferredAudioDevice
                        });

                        console.log('✅ UnifiedAudioRecorder initialized, type:', this.audioRecorderType, 'device:', this.settings.preferredAudioDevice);
                    } else {
                        console.warn('⚠️ UnifiedAudioRecorder not available');
                        this.audioRecorderType = 'none';
                        this.setState({ audioRecorderType: 'none' });
                    }
                    
                    // WhisperAPI initialisieren (wenn API-Key verfügbar)
                    if (this.settings.openaiApiKey && typeof WhisperAPI !== 'undefined') {
                        this.whisperAPI = new WhisperAPI(this.settings.openaiApiKey);
                        console.log('✅ WhisperAPI initialized');
                    } else {
                        console.log('⚠️ WhisperAPI not initialized (no API key or class not available)');
                    }
                    
                    console.log('✅ Audio system initialized');
                } catch (error) {
                    console.error('❌ Audio system failed:', error);
                }
            }

            setupTauriListener() {
                console.log('📡 Setting up Tauri readiness listener...');
                
                // Sofort prüfen falls Tauri schon bereit ist
                if (window.tauriReady) {
                    console.log('✅ Tauri already ready, initializing audio system...');
                    this.initAudioSystem();
                    return;
                }
                
                // Event Listener für Tauri readiness
                window.addEventListener('tauriReady', () => {
                    console.log('🎉 Tauri ready event received, initializing audio system...');
                    this.initAudioSystem();
                    
                    // Panel aktualisieren falls geöffnet
                    this.updateActivePanelIfOpen();
                });
                
                // Fallback nach 3 Sekunden
                setTimeout(() => {
                    if (!window.tauriReady) {
                        console.warn('⚠️ Tauri not ready after 3 seconds, trying anyway...');
                        this.initAudioSystem();
                    }
                }, 3000);
            }
            
            setupUI() {
                console.log('🎮 Setting up UI...');
                this.setupButtons();
                this.setupHotkeys();
                // Dragging wird separat initialisiert
                // Expandable panel removed
                console.log('✅ UI setup complete');
            }

            setupHotkeys() {
                console.log('⌨️ Setting up hotkeys...');
                
                // Hotkey für Recording: F9
                document.addEventListener('keydown', async (e) => {
                    if (e.key === 'F9') {
                        e.preventDefault();
                        console.log('🎹 F9 pressed: Recording toggle');
                        
                        if (this.isRecording) {
                            await this.stopRecording();
                        } else {
                            await this.startRecording();
                        }
                    }
                    
                    // ESC zum Beenden der Aufnahme
                    if (e.key === 'Escape' && this.isRecording) {
                        e.preventDefault();
                        console.log('🎹 ESC pressed: Stopping recording');
                        await this.stopRecording();
                    }
                });
                
                console.log('✅ Hotkeys setup complete:');
                console.log('   📹 F9: Recording toggle');
                console.log('   ⛔ ESC: Stop recording');
            }
            
            setupButtons() {
                // Document Button - öffnet Document Panel
                const docBtn = document.getElementById('documentBtn');
                if (docBtn) {
                    docBtn.addEventListener('click', () => {
                        console.log('📄 Document Button clicked');
                        this.openExpandablePanel('document');
                    });
                }
                
                // AI+ Button - öffnet AI Panel
                const aiBtn = document.getElementById('aiBtn');
                if (aiBtn) {
                    aiBtn.addEventListener('click', () => {
                        console.log('🤖 AI Button clicked - opening panel');
                        this.openExpandablePanel('ai');
                    });
                }
                
                // Select+ Button - öffnet Select Panel
                const selectBtn = document.getElementById('selectBtn');
                if (selectBtn) {
                    selectBtn.addEventListener('click', () => {
                        console.log('🎯 Select Button clicked');
                        this.openExpandablePanel('select');
                    });
                }
            }

            // Settings Management
            loadSettings() {
                try {
                    const savedSettings = localStorage.getItem('meinwort-settings');
                    if (savedSettings) {
                        const parsed = JSON.parse(savedSettings);
                        this.settings = { ...this.settings, ...parsed };
                        console.log('✅ Settings loaded from localStorage');
                    }
                } catch (error) {
                    console.warn('⚠️ Failed to load settings:', error);
                }
            }

            saveSettings() {
                try {
                    localStorage.setItem('meinwort-settings', JSON.stringify(this.settings));
                    console.log('✅ Settings saved to localStorage');
                } catch (error) {
                    console.warn('⚠️ Failed to save settings:', error);
                }
            }

            // Native Desktop Audio System Management
            async testNativeAudioSystem() {
                try {
                    console.log('🎤 Testing native desktop audio system...');
                    
                    if (!this.audioRecorder || this.audioRecorderType !== 'native') {
                        throw new Error('Native Audio-System nicht initialisiert');
                    }
                    
                    // Test native microphone access
                    if (window.__TAURI__ && window.__TAURI__.invoke) {
                        const result = await window.__TAURI__.invoke('check_microphone_access');
                        console.log('✅ Native audio test successful:', result);
                        this.showToast('✅ Desktop-Audio-System funktioniert');
                        return true;
                    } else {
                        throw new Error('Tauri Desktop-APIs nicht verfügbar');
                    }
                } catch (error) {
                    console.error('❌ Native audio test failed:', error);
                    this.showToast('❌ Desktop-Audio-Test fehlgeschlagen: ' + error.message);
                    return false;
                }
            }
            
            setupDragging() {
                console.log('🤏 Setting up window dragging...');
                
                const app = document.getElementById('app');
                
                // Prüfe ob schon initialisiert
                if (app.draggingInitialized) {
                    console.log('🤏 Dragging already initialized, skipping...');
                    return;
                }
                
                let isDragging = false;
                let startX, startY, startPos;
                
                app.addEventListener('mousedown', async (e) => {
                    // Skip if clicking on buttons
                    if (e.target.closest('.button-container')) {
                        console.log('🚫 Button clicked - no drag');
                        return;
                    }
                    
                    // Check if Tauri is available (weniger strenge Prüfung)
                    if (!window.__TAURI__ || !window.__TAURI__.window) {
                        console.log('❌ Tauri window API not available');
                        return;
                    }
                    
                    console.log('🎯 Starting drag...');
                    app.style.cursor = 'grabbing';
                    
                    const appWindow = window.__TAURI__.window.getCurrentWindow();
                    
                    try {
                        // Try native dragging
                        await appWindow.startDragging();
                        console.log('✅ Native drag successful');
                    } catch (error) {
                        console.log('⚠️ Native drag failed:', error.message || error);
                        
                        // Manual dragging fallback
                        isDragging = true;
                        startX = e.clientX;
                        startY = e.clientY;
                        
                        try {
                            startPos = await appWindow.outerPosition();
                            console.log('📍 Manual drag starting at:', startPos);
                        } catch (posError) {
                            console.log('❌ Position failed:', posError.message || posError);
                            isDragging = false;
                        }
                    }
                });
                
                // Manual drag handling
                document.addEventListener('mousemove', async (e) => {
                    if (!isDragging || !startPos || !window.__TAURI__) return;
                    
                    const deltaX = e.clientX - startX;
                    const deltaY = e.clientY - startY;
                    
                    const newPos = {
                        x: startPos.x + deltaX,
                        y: startPos.y + deltaY
                    };
                    
                    try {
                        const appWindow = window.__TAURI__.window.getCurrentWindow();
                        await appWindow.setPosition(newPos);
                    } catch (error) {
                        console.log('❌ setPosition failed:', error.message || error);
                    }
                });
                
                document.addEventListener('mouseup', () => {
                    if (isDragging) {
                        console.log('🎯 Manual drag ended');
                        isDragging = false;
                        startPos = null;
                    }
                    app.style.cursor = 'grab';
                });
                
                // Markiere als initialisiert
                app.draggingInitialized = true;
                console.log('✅ Dragging setup complete');
            }

            // Expandable Panel Management
            async openExpandablePanel(type) {
                const app = document.getElementById('app');
                const panel = document.getElementById('expandablePanel');
                const panelTitle = document.getElementById('panelTitle');
                const panelContent = document.getElementById('panelContent');

                // Tauri-Fenster erweitern
                if (window.tauriReady && window.__TAURI__ && window.__TAURI__.window) {
                    try {
                        const appWindow = window.__TAURI__.window.getCurrentWindow();
                        console.log('🔧 Attempting to resize Tauri window to 600x400...');
                        
                        // Verschiedene APIs probieren
                        await appWindow.setSize(new window.__TAURI__.window.LogicalSize(600, 400));
                        console.log('✅ Tauri window resized to 600x400 with LogicalSize');
                    } catch (error) {
                        console.log('❌ LogicalSize failed, trying object method:', error);
                        try {
                            await appWindow.setSize({ width: 600, height: 400 });
                            console.log('✅ Tauri window resized with object');
                        } catch (error2) {
                            console.log('❌ All resize methods failed:', error2);
                        }
                    }
                } else {
                    console.log('❌ Tauri APIs not available');
                }

                // App erweitern
                app.classList.add('expanded');
                
                // Panel aktivieren
                panel.classList.add('active');
                
                // Content je nach Type laden
                this.loadPanelContent(type, panelTitle, panelContent);

                // Historie, Kosten, Audio-Geräte, Workflows und Audio Processing laden wenn AI-Panel geöffnet wird
                if (type === 'ai') {
                    setTimeout(() => {
                        this.updateRecordingsHistoryUI();
                        this.updateCostStatsUI();
                        this.loadAudioDevices();
                        this.loadWorkflowTemplates();
                        this.loadAudioProcessingPresets();
                    }, 100);
                }

                console.log(`📋 Expandable panel opened: ${type}`);
            }

            loadPanelContent(type, titleElement, contentElement) {
                let title = '';
                let content = '';

                switch(type) {
                    case 'document':
                        title = '📄 Document';
                        content = `
                            <div class="panel-section">
                                <div class="panel-section-title">Export Optionen</div>
                                <button class="panel-button primary">Als PDF exportieren</button>
                                <button class="panel-button">Als Word exportieren</button>
                                <button class="panel-button">Als Text exportieren</button>
                            </div>
                            <div class="panel-section">
                                <div class="panel-section-title">Dokument-Historie</div>
                                <button class="panel-button">Letzte Dokumente</button>
                                <button class="panel-button">Vorlagen verwalten</button>
                            </div>
                        `;
                        break;
                    case 'ai':
                        title = '🤖 AI+ Voice Transcription';
                        content = `
                            <div class="panel-section">
                                <div class="panel-section-title">Desktop Audio-System</div>
                                <div style="color: rgba(255, 255, 255, 0.7); font-size: 11px; margin-bottom: 10px;">
                                    ${this.audioRecorderType === 'native' ? '✅ Native Desktop-Audio bereit' : '❌ Desktop-System wird initialisiert...'}
                                </div>
                                ${this.audioRecorderType !== 'native' ? 
                                    '<button class="panel-button primary" onclick="app.initAudioSystem()">🔄 Audio-System neu laden</button>' : ''}
                            </div>
                            <div class="panel-section">
                                <div class="panel-section-title">Audio-Aufnahme Status</div>
                                <div id="recordingStatus" style="color: rgba(255, 255, 255, 0.7); font-size: 11px; margin-bottom: 10px;">
                                    ${this.isRecording ? '🎤 Aufnahme läuft...' : '⏸️ Bereit für Aufnahme'}
                                </div>
                                <button class="panel-button primary" onclick="app.isRecording ? app.stopRecording() : app.startRecording()">
                                    ${this.isRecording ? '🛑 Aufnahme stoppen' : '🎤 Aufnahme starten'}
                                </button>
                                <button class="panel-button" onclick="app.testWhisperConnection()">🔍 API-Verbindung testen</button>
                            </div>
                            <div class="panel-section">
                                <div class="panel-section-title">🤖 Smart Transkription</div>
                                ${this.currentTranscription ? `
                                    <div style="margin-bottom: 10px;">
                                        <div style="font-size: 10px; color: rgba(255, 255, 255, 0.7); margin-bottom: 4px; display: flex; justify-content: space-between;">
                                            <span>${this.currentTranscription.enhanced ? '✨ AI-Verbessert' : '📝 Original'}</span>
                                            <span>${this.currentTranscription.language || 'de'} • ${this.currentTranscription.duration?.toFixed(1) || '0'}s</span>
                                        </div>
                                        <div style="color: rgba(255, 255, 255, 0.9); font-size: 11px; max-height: 80px; overflow-y: auto; background: rgba(0,0,0,0.3); padding: 8px; border-radius: 6px; margin-bottom: 8px;">
                                            ${this.currentTranscription.text}
                                        </div>
                                        <div style="display: flex; gap: 6px; margin-bottom: 10px;">
                                            <button class="panel-button" onclick="app.copyLastTranscription()" style="flex: 1; font-size: 10px;">
                                                📋 Kopieren
                                            </button>
                                            ${!this.currentTranscription.enhanced ? `
                                                <button class="panel-button" onclick="app.enhanceLastTranscription()" style="flex: 1; font-size: 10px; background: rgba(138, 43, 226, 0.1); border-color: rgba(138, 43, 226, 0.3);" ${this.appState.aiProcessingInProgress ? 'disabled' : ''}>
                                                    ✨ Verbessern
                                                </button>
                                            ` : ''}
                                        </div>
                                    </div>

                                    ${this.currentTranscription.enhancement ? `
                                        <div style="border-top: 1px solid rgba(255, 255, 255, 0.1); padding-top: 10px;">
                                            ${this.currentTranscription.enhancement.summary ? `
                                                <div style="margin-bottom: 8px;">
                                                    <div style="font-size: 10px; color: rgba(255, 215, 0, 0.9); margin-bottom: 4px; display: flex; justify-content: space-between;">
                                                        <span>📄 Zusammenfassung</span>
                                                        <button onclick="app.copySummary(app.currentTranscription.enhancement)" style="background: none; border: none; color: rgba(255, 255, 255, 0.7); cursor: pointer; font-size: 10px;">📋</button>
                                                    </div>
                                                    <div style="font-size: 10px; color: rgba(255, 255, 255, 0.8); background: rgba(255, 215, 0, 0.1); padding: 6px; border-radius: 4px;">
                                                        ${this.currentTranscription.enhancement.summary}
                                                    </div>
                                                </div>
                                            ` : ''}

                                            ${this.currentTranscription.enhancement.keywords.length > 0 ? `
                                                <div style="margin-bottom: 8px;">
                                                    <div style="font-size: 10px; color: rgba(0, 255, 255, 0.9); margin-bottom: 4px; display: flex; justify-content: space-between;">
                                                        <span>🏷️ Schlüsselwörter</span>
                                                        <button onclick="app.copyKeywords(app.currentTranscription.enhancement)" style="background: none; border: none; color: rgba(255, 255, 255, 0.7); cursor: pointer; font-size: 10px;">📋</button>
                                                    </div>
                                                    <div style="font-size: 10px; color: rgba(255, 255, 255, 0.8);">
                                                        ${this.currentTranscription.enhancement.keywords.map(keyword =>
                                                            `<span style="background: rgba(0, 255, 255, 0.1); padding: 2px 6px; border-radius: 3px; margin-right: 4px; margin-bottom: 2px; display: inline-block;">${keyword}</span>`
                                                        ).join('')}
                                                    </div>
                                                </div>
                                            ` : ''}

                                            ${this.currentTranscription.enhancement.action_items.length > 0 ? `
                                                <div style="margin-bottom: 8px;">
                                                    <div style="font-size: 10px; color: rgba(255, 99, 71, 0.9); margin-bottom: 4px; display: flex; justify-content: space-between;">
                                                        <span>✅ Action Items</span>
                                                        <button onclick="app.copyActionItems(app.currentTranscription.enhancement)" style="background: none; border: none; color: rgba(255, 255, 255, 0.7); cursor: pointer; font-size: 10px;">📋</button>
                                                    </div>
                                                    <div style="font-size: 10px; color: rgba(255, 255, 255, 0.8); background: rgba(255, 99, 71, 0.1); padding: 6px; border-radius: 4px;">
                                                        ${this.currentTranscription.enhancement.action_items.join('<br>')}
                                                    </div>
                                                </div>
                                            ` : ''}

                                            <div style="font-size: 9px; color: rgba(255, 255, 255, 0.5); text-align: center;">
                                                Verarbeitet in ${this.currentTranscription.enhancement.processing_time_ms}ms • Konfidenz: ${(this.currentTranscription.enhancement.confidence_score * 100).toFixed(0)}%
                                            </div>
                                        </div>
                                    ` : ''}
                                ` : `
                                    <div style="text-align: center; color: rgba(255, 255, 255, 0.6); padding: 20px; font-size: 11px;">
                                        <span style="font-size: 24px;">🤖</span>
                                        <p>Noch keine Transkription verfügbar</p>
                                        <p style="font-size: 10px; margin-top: 8px;">Drücke F9 für eine Aufnahme</p>
                                    </div>
                                `}
                            </div>
                            <div class="panel-section">
                                <div class="panel-section-title">⚡ Workflow Automation</div>
                                ${this.appState.availableWorkflows.length > 0 ? `
                                    <div style="margin-bottom: 10px;">
                                        <div style="font-size: 10px; color: rgba(255, 255, 255, 0.7); margin-bottom: 8px;">
                                            Verfügbare Workflows (${this.appState.availableWorkflows.filter(w => w.enabled).length}/${this.appState.availableWorkflows.length} aktiv):
                                        </div>
                                        ${this.appState.availableWorkflows.map(workflow => `
                                            <div style="background: rgba(0,0,0,0.3); border: 1px solid rgba(255,255,255,0.1); border-radius: 6px; padding: 8px; margin-bottom: 6px;">
                                                <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 4px;">
                                                    <div style="flex: 1;">
                                                        <div style="font-size: 11px; color: rgba(255, 255, 255, 0.9); margin-bottom: 2px;">
                                                            ${workflow.enabled ? '✅' : '⏸️'} ${workflow.name}
                                                        </div>
                                                        <div style="font-size: 9px; color: rgba(255, 255, 255, 0.6);">
                                                            ${workflow.description}
                                                        </div>
                                                        <div style="font-size: 8px; color: rgba(255, 255, 255, 0.5); margin-top: 2px;">
                                                            Trigger: ${workflow.trigger.trigger_type} • ${workflow.actions.length} Aktionen
                                                        </div>
                                                    </div>
                                                    <div style="display: flex; gap: 4px; margin-left: 8px;">
                                                        <button onclick="app.testWorkflow(${JSON.stringify(workflow).replace(/"/g, '&quot;')})" style="background: rgba(0, 255, 0, 0.1); border: 1px solid rgba(0, 255, 0, 0.3); border-radius: 4px; padding: 4px 6px; color: rgba(0, 255, 0, 0.9); font-size: 8px; cursor: pointer;" ${this.appState.workflowExecutionInProgress ? 'disabled' : ''}>
                                                            🧪
                                                        </button>
                                                        <button onclick="app.toggleWorkflow('${workflow.id}')" style="background: rgba(0, 0, 255, 0.1); border: 1px solid rgba(0, 0, 255, 0.3); border-radius: 4px; padding: 4px 6px; color: rgba(0, 0, 255, 0.9); font-size: 8px; cursor: pointer;">
                                                            ${workflow.enabled ? '⏸️' : '▶️'}
                                                        </button>
                                                    </div>
                                                </div>
                                                ${workflow.enabled && workflow.actions.length > 0 ? `
                                                    <div style="border-top: 1px solid rgba(255, 255, 255, 0.1); padding-top: 4px; margin-top: 4px;">
                                                        <div style="font-size: 8px; color: rgba(255, 255, 255, 0.6);">
                                                            Aktionen: ${workflow.actions.map(action => action.name).join(', ')}
                                                        </div>
                                                    </div>
                                                ` : ''}
                                            </div>
                                        `).join('')}
                                    </div>

                                    ${this.appState.lastWorkflowExecution ? `
                                        <div style="border-top: 1px solid rgba(255, 255, 255, 0.1); padding-top: 8px; margin-top: 8px;">
                                            <div style="font-size: 10px; color: rgba(255, 255, 255, 0.7); margin-bottom: 4px;">
                                                Letzte Ausführung:
                                            </div>
                                            <div style="background: rgba(${this.appState.lastWorkflowExecution.success ? '0, 255, 0' : '255, 165, 0'}, 0.1); border: 1px solid rgba(${this.appState.lastWorkflowExecution.success ? '0, 255, 0' : '255, 165, 0'}, 0.3); border-radius: 4px; padding: 6px; font-size: 9px;">
                                                <div style="color: rgba(255, 255, 255, 0.9);">
                                                    ${this.appState.lastWorkflowExecution.success ? '✅' : '⚠️'} ${this.appState.lastWorkflowExecution.executed_actions.length} Aktionen erfolgreich
                                                    ${this.appState.lastWorkflowExecution.failed_actions.length > 0 ? `, ${this.appState.lastWorkflowExecution.failed_actions.length} fehlgeschlagen` : ''}
                                                </div>
                                                <div style="color: rgba(255, 255, 255, 0.6); margin-top: 2px;">
                                                    ${this.appState.lastWorkflowExecution.execution_time_ms}ms
                                                </div>
                                            </div>
                                        </div>
                                    ` : ''}
                                ` : `
                                    <div style="text-align: center; color: rgba(255, 255, 255, 0.6); padding: 20px; font-size: 11px;">
                                        <span style="font-size: 24px;">⚡</span>
                                        <p>Lade Workflows...</p>
                                    </div>
                                `}
                            </div>
                            <div class="panel-section">
                                <div class="panel-section-title">💰 Kosten-Übersicht</div>
                                <div id="costStats" style="margin-bottom: 15px;">
                                    <div style="text-align: center; color: rgba(255, 255, 255, 0.6); padding: 10px;">
                                        <span style="font-size: 16px;">💰</span>
                                        <p style="font-size: 11px;">Lade Kosten...</p>
                                    </div>
                                </div>
                            </div>
                            <div class="panel-section">
                                <div class="panel-section-title">
                                    📋 Aufnahme-Historie
                                    <span onclick="app.updateRecordingsHistoryUI()" style="cursor: pointer; margin-left: 8px; opacity: 0.7; font-size: 12px;" title="Historie aktualisieren">🔄</span>
                                </div>
                                <div id="recordingsHistory" style="max-height: 200px; overflow-y: auto;">
                                    <div style="text-align: center; color: rgba(255, 255, 255, 0.6); padding: 20px;">
                                        <span style="font-size: 24px;">🎤</span>
                                        <p>Lade Historie...</p>
                                    </div>
                                </div>
                            </div>
                            <div class="panel-section">
                                <div class="panel-section-title">🔑 API-Konfiguration</div>
                                ${this.settings.openaiApiKey ? `
                                    <div style="background: rgba(0, 255, 0, 0.1); border: 1px solid rgba(0, 255, 0, 0.3); border-radius: 6px; padding: 8px; margin-bottom: 8px; font-size: 11px; color: rgba(0, 255, 0, 0.9);">
                                        ✅ API-Key gespeichert (${this.settings.openaiApiKey.substring(0, 7)}...)
                                    </div>
                                    <div style="display: flex; gap: 8px; margin-bottom: 8px;">
                                        <button class="panel-button" onclick="app.clearApiKey()" style="flex: 1; background: rgba(255, 0, 0, 0.1); border-color: rgba(255, 0, 0, 0.3);">
                                            🗑️ Löschen
                                        </button>
                                        <button class="panel-button" onclick="app.testWhisperConnection()" style="flex: 1; background: rgba(0, 255, 0, 0.1); border-color: rgba(0, 255, 0, 0.3);">
                                            🧪 Testen
                                        </button>
                                    </div>
                                ` : `
                                    <div style="background: rgba(255, 165, 0, 0.1); border: 1px solid rgba(255, 165, 0, 0.3); border-radius: 6px; padding: 8px; margin-bottom: 8px; font-size: 11px; color: rgba(255, 165, 0, 0.9);">
                                        ⚠️ Kein API-Key gespeichert
                                    </div>
                                `}
                                <input type="password" id="apiKeyInput" placeholder="sk-..." style="width: 100%; padding: 8px; margin-bottom: 8px; background: rgba(0,0,0,0.3); border: 1px solid rgba(255,255,255,0.2); border-radius: 6px; color: white; font-size: 11px;"
                                       value="">
                                <button class="panel-button primary" onclick="app.setApiKeyFromInput()">🔑 API-Key ${this.settings.openaiApiKey ? 'ändern' : 'setzen'}</button>
                            </div>
                            <div class="panel-section">
                                <div class="panel-section-title">🎤 Audio-Geräte</div>
                                <div style="margin-bottom: 10px;">
                                    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 8px;">
                                        <span style="font-size: 11px; color: rgba(255, 255, 255, 0.8);">Verfügbare Mikrofone:</span>
                                        <button onclick="app.refreshAudioDevices()" style="background: none; border: none; color: rgba(255, 255, 255, 0.7); cursor: pointer; font-size: 12px;" title="Audio-Geräte aktualisieren">🔄</button>
                                    </div>
                                    <div id="audioDevicesList" style="max-height: 120px; overflow-y: auto;">
                                        ${this.appState.availableAudioDevices.length > 0 ?
                                            this.appState.availableAudioDevices.map(device => {
                                                const status = this.getAudioDeviceStatus(device);
                                                return `
                                                    <div style="background: rgba(0,0,0,0.3); border: 1px solid rgba(255,255,255,0.1); border-radius: 6px; padding: 8px; margin-bottom: 6px; display: flex; justify-content: space-between; align-items: center;">
                                                        <div style="flex: 1;">
                                                            <div style="font-size: 11px; color: rgba(255, 255, 255, 0.9); margin-bottom: 2px;">
                                                                ${status.icon} ${device.name}
                                                            </div>
                                                            <div style="font-size: 9px; color: ${status.color};">
                                                                ${status.text}
                                                            </div>
                                                        </div>
                                                        <div style="display: flex; gap: 4px;">
                                                            ${device.is_available ? `
                                                                <button onclick="app.testAudioDevice('${device.id}')" style="background: rgba(0, 255, 0, 0.1); border: 1px solid rgba(0, 255, 0, 0.3); border-radius: 4px; padding: 4px 8px; color: rgba(0, 255, 0, 0.9); font-size: 9px; cursor: pointer;" ${this.appState.audioDeviceTestInProgress ? 'disabled' : ''}>
                                                                    🧪
                                                                </button>
                                                                ${device.id !== this.settings.preferredAudioDevice ? `
                                                                    <button onclick="app.setPreferredAudioDevice('${device.id}')" style="background: rgba(0, 0, 255, 0.1); border: 1px solid rgba(0, 0, 255, 0.3); border-radius: 4px; padding: 4px 8px; color: rgba(0, 0, 255, 0.9); font-size: 9px; cursor: pointer;">
                                                                        ✓
                                                                    </button>
                                                                ` : ''}
                                                            ` : ''}
                                                        </div>
                                                    </div>
                                                `;
                                            }).join('') :
                                            '<div style="text-align: center; color: rgba(255, 255, 255, 0.6); padding: 20px; font-size: 11px;">Lade Audio-Geräte...</div>'
                                        }
                                    </div>
                                </div>
                            </div>
                            <div class="panel-section">
                                <div class="panel-section-title">⚙️ Aufnahme-Einstellungen</div>
                                <div style="margin-bottom: 10px;">
                                    <label style="display: block; font-size: 11px; color: rgba(255, 255, 255, 0.8); margin-bottom: 4px;">Audio-Qualität:</label>
                                    <select id="audioQualitySelect" onchange="app.updateSetting('audioQuality', this.value)" style="width: 100%; padding: 6px; background: rgba(0,0,0,0.3); border: 1px solid rgba(255,255,255,0.2); border-radius: 4px; color: white; font-size: 11px;">
                                        <option value="low" ${this.settings.audioQuality === 'low' ? 'selected' : ''}>Niedrig (schneller)</option>
                                        <option value="medium" ${this.settings.audioQuality === 'medium' ? 'selected' : ''}>Mittel</option>
                                        <option value="high" ${this.settings.audioQuality === 'high' ? 'selected' : ''}>Hoch (bessere Qualität)</option>
                                    </select>
                                </div>
                                <div style="margin-bottom: 10px;">
                                    <label style="display: block; font-size: 11px; color: rgba(255, 255, 255, 0.8); margin-bottom: 4px;">Sprache:</label>
                                    <select id="languageSelect" onchange="app.updateSetting('language', this.value)" style="width: 100%; padding: 6px; background: rgba(0,0,0,0.3); border: 1px solid rgba(255,255,255,0.2); border-radius: 4px; color: white; font-size: 11px;">
                                        <option value="de" ${this.settings.language === 'de' ? 'selected' : ''}>🇩🇪 Deutsch</option>
                                        <option value="en" ${this.settings.language === 'en' ? 'selected' : ''}>🇺🇸 English</option>
                                        <option value="es" ${this.settings.language === 'es' ? 'selected' : ''}>🇪🇸 Español</option>
                                        <option value="fr" ${this.settings.language === 'fr' ? 'selected' : ''}>🇫🇷 Français</option>
                                        <option value="it" ${this.settings.language === 'it' ? 'selected' : ''}>🇮🇹 Italiano</option>
                                    </select>
                                </div>
                                <div style="margin-bottom: 10px;">
                                    <label style="display: block; font-size: 11px; color: rgba(255, 255, 255, 0.8); margin-bottom: 4px;">Max. Aufnahmedauer (Sekunden):</label>
                                    <input type="number" id="maxDurationInput" min="10" max="3600" value="${this.settings.maxRecordingDuration}" onchange="app.updateSetting('maxRecordingDuration', parseInt(this.value))" style="width: 100%; padding: 6px; background: rgba(0,0,0,0.3); border: 1px solid rgba(255,255,255,0.2); border-radius: 4px; color: white; font-size: 11px;">
                                </div>
                            </div>
                            <div class="panel-section">
                                <div class="panel-section-title">🔧 Verhalten</div>
                                <div style="margin-bottom: 8px;">
                                    <label style="display: flex; align-items: center; font-size: 11px; color: rgba(255, 255, 255, 0.9); cursor: pointer;">
                                        <input type="checkbox" ${this.settings.autoClipboard ? 'checked' : ''} onchange="app.updateSetting('autoClipboard', this.checked)" style="margin-right: 8px;">
                                        📋 Automatisch in Zwischenablage kopieren
                                    </label>
                                </div>
                                <div style="margin-bottom: 8px;">
                                    <label style="display: flex; align-items: center; font-size: 11px; color: rgba(255, 255, 255, 0.9); cursor: pointer;">
                                        <input type="checkbox" ${this.settings.showNotifications ? 'checked' : ''} onchange="app.updateSetting('showNotifications', this.checked)" style="margin-right: 8px;">
                                        🔔 Benachrichtigungen anzeigen
                                    </label>
                                </div>
                                <div style="margin-bottom: 8px;">
                                    <label style="display: flex; align-items: center; font-size: 11px; color: rgba(255, 255, 255, 0.9); cursor: pointer;">
                                        <input type="checkbox" ${this.settings.autoSaveRecordings ? 'checked' : ''} onchange="app.updateSetting('autoSaveRecordings', this.checked)" style="margin-right: 8px;">
                                        💾 Aufnahmen automatisch speichern
                                    </label>
                                </div>
                                <div style="margin-bottom: 10px;">
                                    <label style="display: block; font-size: 11px; color: rgba(255, 255, 255, 0.8); margin-bottom: 4px;">💰 Kosten-Warnung ab ($ pro Tag):</label>
                                    <input type="number" id="costWarningInput" min="0" max="100" step="0.1" value="${this.settings.costWarningThreshold}" onchange="app.updateSetting('costWarningThreshold', parseFloat(this.value))" style="width: 100%; padding: 6px; background: rgba(0,0,0,0.3); border: 1px solid rgba(255,255,255,0.2); border-radius: 4px; color: white; font-size: 11px;">
                                </div>
                            </div>
                            <div class="panel-section">
                                <div class="panel-section-title">🤖 AI-Features</div>
                                <div style="margin-bottom: 8px;">
                                    <label style="display: flex; align-items: center; font-size: 11px; color: rgba(255, 255, 255, 0.9); cursor: pointer;">
                                        <input type="checkbox" ${this.settings.enableAIEnhancement ? 'checked' : ''} onchange="app.updateSetting('enableAIEnhancement', this.checked)" style="margin-right: 8px;">
                                        ✨ Automatische Text-Verbesserung
                                    </label>
                                </div>
                                <div style="margin-bottom: 8px;">
                                    <label style="display: flex; align-items: center; font-size: 11px; color: rgba(255, 255, 255, 0.9); cursor: pointer;">
                                        <input type="checkbox" ${this.settings.autoGenerateSummary ? 'checked' : ''} onchange="app.updateSetting('autoGenerateSummary', this.checked)" style="margin-right: 8px;">
                                        📄 Automatische Zusammenfassungen
                                    </label>
                                </div>
                                <div style="margin-bottom: 8px;">
                                    <label style="display: flex; align-items: center; font-size: 11px; color: rgba(255, 255, 255, 0.9); cursor: pointer;">
                                        <input type="checkbox" ${this.settings.autoExtractKeywords ? 'checked' : ''} onchange="app.updateSetting('autoExtractKeywords', this.checked)" style="margin-right: 8px;">
                                        🏷️ Automatische Schlüsselwörter
                                    </label>
                                </div>
                                <div style="margin-bottom: 8px;">
                                    <label style="display: flex; align-items: center; font-size: 11px; color: rgba(255, 255, 255, 0.9); cursor: pointer;">
                                        <input type="checkbox" ${this.settings.autoDetectActionItems ? 'checked' : ''} onchange="app.updateSetting('autoDetectActionItems', this.checked)" style="margin-right: 8px;">
                                        ✅ Automatische Action Items
                                    </label>
                                </div>
                            </div>
                            <div class="panel-section">
                                <div class="panel-section-title">⚡ Workflow Automation</div>
                                <div style="margin-bottom: 8px;">
                                    <label style="display: flex; align-items: center; font-size: 11px; color: rgba(255, 255, 255, 0.9); cursor: pointer;">
                                        <input type="checkbox" ${this.settings.enableWorkflowAutomation ? 'checked' : ''} onchange="app.updateSetting('enableWorkflowAutomation', this.checked)" style="margin-right: 8px;">
                                        ⚡ Workflow Automation aktivieren
                                    </label>
                                </div>
                                <div style="margin-bottom: 8px;">
                                    <label style="display: flex; align-items: center; font-size: 11px; color: rgba(255, 255, 255, 0.9); cursor: pointer;">
                                        <input type="checkbox" ${this.settings.autoExecuteWorkflows ? 'checked' : ''} onchange="app.updateSetting('autoExecuteWorkflows', this.checked)" style="margin-right: 8px;">
                                        🔄 Workflows automatisch ausführen
                                    </label>
                                </div>
                            </div>
                            <div class="panel-section">
                                <div class="panel-section-title">🎵 Audio Processing</div>
                                <div style="margin-bottom: 8px;">
                                    <label style="display: flex; align-items: center; font-size: 11px; color: rgba(255, 255, 255, 0.9); cursor: pointer;">
                                        <input type="checkbox" ${this.settings.enableAudioProcessing ? 'checked' : ''} onchange="app.updateSetting('enableAudioProcessing', this.checked)" style="margin-right: 8px;">
                                        🎵 Audio Processing aktivieren
                                    </label>
                                </div>
                                <div style="margin-bottom: 8px;">
                                    <label style="display: block; font-size: 11px; color: rgba(255, 255, 255, 0.8); margin-bottom: 4px;">Preset:</label>
                                    <select onchange="app.updateSetting('audioProcessingPreset', this.value)" style="width: 100%; padding: 6px; background: rgba(0,0,0,0.3); border: 1px solid rgba(255,255,255,0.2); border-radius: 4px; color: white; font-size: 11px;">
                                        <option value="off" ${this.settings.audioProcessingPreset === 'off' ? 'selected' : ''}>🔇 Aus</option>
                                        <option value="light" ${this.settings.audioProcessingPreset === 'light' ? 'selected' : ''}>🔉 Leicht</option>
                                        <option value="standard" ${this.settings.audioProcessingPreset === 'standard' ? 'selected' : ''}>🔊 Standard</option>
                                        <option value="aggressive" ${this.settings.audioProcessingPreset === 'aggressive' ? 'selected' : ''}>🎵 Aggressiv</option>
                                    </select>
                                </div>
                            </div>
                            <div class="panel-section">
                                <div class="panel-section-title">🔄 Einstellungen verwalten</div>
                                <div style="display: flex; gap: 8px; margin-bottom: 8px;">
                                    <button class="panel-button" onclick="app.exportSettings()" style="flex: 1;">
                                        📤 Exportieren
                                    </button>
                                    <button class="panel-button" onclick="document.getElementById('importFile').click()" style="flex: 1;">
                                        📥 Importieren
                                    </button>
                                </div>
                                <button class="panel-button" onclick="app.resetSettings()" style="width: 100%; background: rgba(255, 165, 0, 0.1); border-color: rgba(255, 165, 0, 0.3);">
                                    🔄 Auf Standard zurücksetzen
                                </button>
                                <input type="file" id="importFile" accept=".json" onchange="app.importSettings(this.files[0])" style="display: none;">
                            </div>
                        `;
                        break;
                    case 'select':
                        title = '🎯 Select+ Tools';
                        content = `
                            <div class="panel-section">
                                <div class="panel-section-title">Text-Auswahl</div>
                                <button class="panel-button primary">Smart Select</button>
                                <button class="panel-button">Bereich markieren</button>
                                <button class="panel-button">Automatische Erkennung</button>
                            </div>
                            <div class="panel-section">
                                <div class="panel-section-title">Aktionen</div>
                                <button class="panel-button">Kopieren & Einfügen</button>
                                <button class="panel-button">Text formatieren</button>
                                <button class="panel-button">Clipboard-Manager</button>
                            </div>
                        `;
                        break;
                }

                titleElement.textContent = title;
                contentElement.innerHTML = content;
            }

            
            // Audio Recording Methods
            async startRecording() {
                console.log('🎤 StartRecording called with recorder type:', this.audioRecorderType);

                // State Management: Prüfe aktuellen Zustand
                if (this.appState.recordingState !== 'IDLE') {
                    console.warn('⚠️ Cannot start recording, current state:', this.appState.recordingState);
                    this.showToast(`⚠️ Aufnahme nicht möglich: ${this.appState.recordingState}`);
                    return;
                }

                // Prüfe ob Audio-System bereit ist
                if (!this.audioRecorder || this.audioRecorderType !== 'native') {
                    console.log('🔄 Audio system not ready, trying to reinitialize...');

                    // Versuche Audio-System neu zu initialisieren
                    await this.initAudioSystem();

                    if (!this.audioRecorder || this.audioRecorderType !== 'native') {
                        this.setState({ lastError: new Error('Desktop-Audio-System nicht verfügbar') });
                        this.setRecordingState('ERROR');
                        return;
                    }
                }

                try {
                    console.log('🎤 Starting audio recording...');

                    // State: Recording starten
                    this.setRecordingState('RECORDING');

                    await this.audioRecorder.startRecording();
                    
                    // Visualizer in Recording-Modus setzen
                    if (this.visualizer) {
                        this.visualizer.setRecordingState(true);
                    }
                    
                    // Real-time Audio-Feedback starten
                    this.startAudioFeedback();
                    
                    // UI aktualisieren
                    this.updateRecordingUI(true);
                    
                    this.showToast('🎤 Aufnahme gestartet');
                    console.log('✅ Recording started successfully');
                    
                } catch (error) {
                    console.error('❌ Failed to start recording:', error);
                    this.setState({ lastError: error });
                    this.setRecordingState('ERROR');
                }
            }

            async stopRecording() {
                // State Management: Prüfe aktuellen Zustand
                if (this.appState.recordingState !== 'RECORDING') {
                    console.warn('⚠️ No recording in progress, current state:', this.appState.recordingState);
                    return;
                }

                try {
                    // State: Processing starten
                    this.setRecordingState('PROCESSING');

                    console.log('🛑 Stopping audio recording...');
                    let audioBlob = await this.audioRecorder.stopRecording();

                    // Audio Processing anwenden
                    if (this.settings.enableAudioProcessing) {
                        audioBlob = await this.processAudioData(audioBlob);
                    }
                    
                    // Visualizer zurück in IDLE-Modus
                    if (this.visualizer) {
                        this.visualizer.setRecordingState(false);
                    }
                    
                    // Real-time Audio-Feedback stoppen
                    this.stopAudioFeedback();
                    
                    // UI aktualisieren
                    this.updateRecordingUI(false);
                    
                    this.showToast('🛑 Aufnahme beendet');
                    console.log('✅ Recording stopped, audio blob size:', audioBlob.size);
                    
                    // Transkription starten (wenn WhisperAPI verfügbar)
                    if (this.whisperAPI && audioBlob.size > 0) {
                        // State: Transcribing
                        this.setRecordingState('TRANSCRIBING');
                        await this.transcribeAudio(audioBlob);
                    } else {
                        console.log('⚠️ WhisperAPI not available or empty audio');

                        // Auch ohne Transkription speichern wir die Aufnahme
                        console.log('💾 Saving recording without transcription...');
                        await this.saveRecordingToHistory({
                            duration_seconds: this.getRecordingDuration() / 1000,
                            transcription: 'Transkription nicht verfügbar',
                            cost_usd: 0,
                            file_path: null
                        });

                        // State: Completed
                        this.setRecordingState('COMPLETED');
                        setTimeout(() => this.setRecordingState('IDLE'), 2000);
                    }
                    
                    return audioBlob;
                    
                } catch (error) {
                    console.error('❌ Failed to stop recording:', error);
                    this.setState({ lastError: error });
                    this.setRecordingState('ERROR');
                    
                    // Sicherheitshalber Visualizer zurücksetzen
                    if (this.visualizer) {
                        this.visualizer.setRecordingState(false);
                    }
                    this.updateRecordingUI(false);
                }
            }

            async transcribeAudio(audioBlob) {
                try {
                    this.showToast('🔄 Transkription läuft...');
                    console.log('🔄 Starting transcription...');
                    
                    const result = await this.whisperAPI.transcribeAudio(audioBlob, {
                        language: 'de', // Deutsch als Standard
                        response_format: 'verbose_json'
                    });
                    
                    // Transkription speichern
                    this.currentTranscription = {
                        ...result.result,
                        timestamp: new Date(),
                        cost: result.usage.cost_usd,
                        enhanced: false
                    };

                    console.log('✅ Transcription completed:', {
                        text: result.result.text.substring(0, 100) + '...',
                        language: result.result.language,
                        cost: `$${result.usage.cost_usd.toFixed(4)}`
                    });

                    // Automatische AI-Enhancement wenn aktiviert
                    if (this.settings.enableAIEnhancement && result.result.text.length > 10) {
                        console.log('🤖 Starting automatic AI enhancement...');
                        const enhancement = await this.enhanceTranscription(result.result.text);
                        if (enhancement) {
                            this.currentTranscription.text = enhancement.enhanced_text;
                            this.currentTranscription.enhanced = true;
                            this.currentTranscription.enhancement = enhancement;
                            console.log('✅ Automatic AI enhancement completed');
                        }
                    }

                    // Aufnahme zur History hinzufügen
                    this.recordings.push({
                        timestamp: new Date(),
                        transcription: result.result,
                        usage: result.usage,
                        audioBlob: audioBlob
                    });

                    // UI aktualisieren um neue Transkription anzuzeigen
                    this.updateTranscriptionUI();

                    this.showToast(`✅ Transkription: "${result.result.text.substring(0, 50)}..."`);

                    // In die Zwischenablage kopieren (wenn aktiviert)
                    if (this.settings.autoClipboard && result.result.text) {
                        await this.copyToClipboard(result.result.text);
                    }

                    // Aufnahme in der Datenbank speichern
                    console.log('💾 Saving recording with transcription...');
                    await this.saveRecordingToHistory({
                        duration_seconds: result.result.duration || this.getRecordingDuration() / 1000,
                        transcription: result.result.text,
                        cost_usd: result.usage.cost_usd,
                        file_path: null
                    });

                    // Workflow Automation ausführen
                    if (this.settings.enableWorkflowAutomation) {
                        const workflowData = {
                            text: this.currentTranscription.text,
                            duration: this.currentTranscription.duration,
                            cost: this.currentTranscription.cost,
                            language: this.currentTranscription.language,
                            timestamp: new Date().toISOString(),
                            enhanced: this.currentTranscription.enhanced,
                            enhancement: this.currentTranscription.enhancement
                        };

                        // Workflows im Hintergrund ausführen
                        setTimeout(() => {
                            this.executeActiveWorkflows(workflowData);
                        }, 500);
                    }

                    // State: Completed
                    this.setRecordingState('COMPLETED');
                    setTimeout(() => this.setRecordingState('IDLE'), 3000);
                    
                } catch (error) {
                    console.error('❌ Transcription failed:', error);
                    this.setState({ lastError: error });
                    this.setRecordingState('ERROR');
                }
            }

            async copyToClipboard(text) {
                try {
                    // Erst Tauri-eigene Clipboard API versuchen
                    if (window.__TAURI__ && window.__TAURI__.clipboard && window.__TAURI__.clipboard.writeText) {
                        await window.__TAURI__.clipboard.writeText(text);
                        console.log('✅ Text copied to clipboard (Tauri)');
                        this.showToast('📋 Text in Zwischenablage kopiert');
                        return;
                    }
                    
                    // Fallback zu Web API
                    await navigator.clipboard.writeText(text);
                    console.log('✅ Text copied to clipboard (Web)');
                    this.showToast('📋 Text in Zwischenablage kopiert');
                } catch (error) {
                    console.warn('⚠️ Clipboard not available in desktop context:', error.message);
                    
                    // Zeige den Text im Toast für manuelles Kopieren
                    this.showToast(`📋 Text: "${text.substring(0, 50)}..."`);
                }
            }

            updateRecordingUI(recording) {
                // Update recording status in panel if open
                const recordingStatus = document.getElementById('recordingStatus');
                if (recordingStatus) {
                    recordingStatus.textContent = recording ? '🎤 Aufnahme läuft...' : '⏸️ Bereit für Aufnahme';
                }

                // Update recording button in panel if exists
                const recordingBtn = document.querySelector('[onclick*="startRecording"], [onclick*="stopRecording"]');
                if (recordingBtn) {
                    recordingBtn.textContent = recording ? '🛑 Aufnahme stoppen' : '🎤 Aufnahme starten';
                    recordingBtn.onclick = recording ? () => this.stopRecording() : () => this.startRecording();
                }
            }

            updateTranscriptionUI() {
                // Update transcription display in panel if open
                const lastTranscription = document.getElementById('lastTranscription');
                if (lastTranscription) {
                    lastTranscription.textContent = this.currentTranscription ? this.currentTranscription.text : 'Noch keine Transkription verfügbar';
                }

                // Update copy button state
                const copyBtn = document.querySelector('[onclick*="copyLastTranscription"]');
                if (copyBtn) {
                    copyBtn.disabled = !this.currentTranscription;
                }

                // Update recordings history and cost stats if panel is open
                this.updateRecordingsHistoryUI();
                this.updateCostStatsUI();

                console.log('✅ Transcription UI updated');
            }

            async saveRecordingToHistory(recordingData) {
                try {
                    console.log('💾 Saving recording to history...', recordingData);
                    const tauriInvoke = window.__TAURI__?.invoke || window.__TAURI__?.core?.invoke;

                    if (!tauriInvoke) {
                        console.error('❌ Tauri invoke not available');
                        return;
                    }

                    // Packe alle Parameter in ein input Objekt
                    const recordingId = await tauriInvoke('save_recording', {
                        input: {
                            duration_seconds: recordingData.duration_seconds,
                            transcription: recordingData.transcription,
                            cost_usd: recordingData.cost_usd,
                            file_path: recordingData.file_path
                        }
                    });
                    console.log('✅ Recording saved with ID:', recordingId);

                    // Update UI if history panel is open
                    this.updateRecordingsHistoryUI();

                } catch (error) {
                    console.error('❌ Failed to save recording:', error);
                    console.error('Error details:', error);
                }
            }

            async loadRecordingsHistory(limit = 10) {
                try {
                    console.log('📋 Loading recordings history...');
                    const tauriInvoke = window.__TAURI__?.invoke || window.__TAURI__?.core?.invoke;

                    const recordings = await tauriInvoke('get_recordings_history', { limit });
                    console.log('✅ Loaded recordings:', recordings.length);

                    return recordings;
                } catch (error) {
                    console.error('❌ Failed to load recordings:', error);
                    return [];
                }
            }

            async deleteRecording(recordingId) {
                try {
                    console.log('🗑️ Deleting recording:', recordingId);
                    const tauriInvoke = window.__TAURI__?.invoke || window.__TAURI__?.core?.invoke;

                    await tauriInvoke('delete_recording', {
                        id: recordingId
                    });
                    console.log('✅ Recording deleted');

                    // Update UI
                    this.updateRecordingsHistoryUI();
                    this.showToast('🗑️ Aufnahme gelöscht');

                } catch (error) {
                    console.error('❌ Failed to delete recording:', error);
                    this.showToast('❌ Fehler beim Löschen');
                }
            }

            async updateRecordingsHistoryUI() {
                const historyContainer = document.getElementById('recordingsHistory');
                if (!historyContainer) return;

                const recordings = await this.loadRecordingsHistory(10);

                if (recordings.length === 0) {
                    historyContainer.innerHTML = `
                        <div style="text-align: center; color: rgba(255, 255, 255, 0.6); padding: 20px;">
                            <span style="font-size: 24px;">🎤</span>
                            <p>Noch keine Aufnahmen vorhanden</p>
                            <p style="font-size: 12px;">Drücke F9 für deine erste Aufnahme</p>
                        </div>
                    `;
                    return;
                }

                historyContainer.innerHTML = recordings.map(recording => {
                    const date = new Date(recording.timestamp);
                    const timeStr = date.toLocaleString('de-DE', {
                        day: '2-digit',
                        month: '2-digit',
                        hour: '2-digit',
                        minute: '2-digit'
                    });

                    const preview = recording.transcription.length > 50
                        ? recording.transcription.substring(0, 50) + '...'
                        : recording.transcription;

                    return `
                        <div class="recording-item" style="background: rgba(0,0,0,0.3); margin-bottom: 8px; padding: 10px; border-radius: 6px; border-left: 3px solid #ffd700;">
                            <div style="display: flex; justify-content: between; align-items: flex-start; margin-bottom: 6px;">
                                <div style="flex: 1;">
                                    <div style="font-size: 11px; color: rgba(255, 255, 255, 0.7); margin-bottom: 4px;">
                                        📅 ${timeStr} • ⏱️ ${recording.duration_seconds.toFixed(1)}s • 💰 $${recording.cost_usd.toFixed(4)}
                                    </div>
                                    <div style="font-size: 12px; color: rgba(255, 255, 255, 0.9); line-height: 1.3;">
                                        ${preview}
                                    </div>
                                </div>
                                <div style="margin-left: 10px;">
                                    <button onclick="app.copyRecordingText('${recording.transcription.replace(/'/g, "\\'")}')"
                                            style="background: rgba(255, 215, 0, 0.2); border: 1px solid rgba(255, 215, 0, 0.3); color: #ffd700; padding: 4px 8px; border-radius: 4px; font-size: 10px; margin-right: 4px; cursor: pointer;">
                                        📋
                                    </button>
                                    <button onclick="app.deleteRecording(${recording.id})"
                                            style="background: rgba(255, 0, 0, 0.2); border: 1px solid rgba(255, 0, 0, 0.3); color: #ff6b6b; padding: 4px 8px; border-radius: 4px; font-size: 10px; cursor: pointer;">
                                        🗑️
                                    </button>
                                </div>
                            </div>
                        </div>
                    `;
                }).join('');
            }

            async copyRecordingText(text) {
                try {
                    await this.copyToClipboard(text);
                    this.showToast('📋 Text kopiert');
                } catch (error) {
                    console.error('❌ Failed to copy text:', error);
                    this.showToast('❌ Kopieren fehlgeschlagen');
                }
            }

            async loadCostStats() {
                try {
                    console.log('💰 Loading cost statistics...');
                    const tauriInvoke = window.__TAURI__?.invoke || window.__TAURI__?.core?.invoke;

                    const stats = await tauriInvoke('get_cost_stats');
                    console.log('✅ Cost stats loaded:', stats);

                    return stats;
                } catch (error) {
                    console.error('❌ Failed to load cost stats:', error);
                    return null;
                }
            }

            async updateCostStatsUI() {
                const costContainer = document.getElementById('costStats');
                if (!costContainer) return;

                const stats = await this.loadCostStats();

                if (!stats) {
                    costContainer.innerHTML = `
                        <div style="text-align: center; color: rgba(255, 0, 0, 0.6); padding: 10px;">
                            <span style="font-size: 16px;">❌</span>
                            <p style="font-size: 11px;">Kosten-Daten nicht verfügbar</p>
                        </div>
                    `;
                    return;
                }

                // Warnung bei hohen Kosten
                const todayWarning = stats.today_cost > 1.0 ? '⚠️' : '';
                const weekWarning = stats.week_cost > 5.0 ? '⚠️' : '';

                costContainer.innerHTML = `
                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 8px; margin-bottom: 10px;">
                        <div style="background: rgba(0,0,0,0.3); padding: 8px; border-radius: 6px; text-align: center;">
                            <div style="font-size: 10px; color: rgba(255, 255, 255, 0.7); margin-bottom: 2px;">Heute ${todayWarning}</div>
                            <div style="font-size: 12px; color: #ffd700; font-weight: bold;">$${stats.today_cost.toFixed(4)}</div>
                            <div style="font-size: 9px; color: rgba(255, 255, 255, 0.5);">${stats.today_recordings} Aufnahmen</div>
                        </div>
                        <div style="background: rgba(0,0,0,0.3); padding: 8px; border-radius: 6px; text-align: center;">
                            <div style="font-size: 10px; color: rgba(255, 255, 255, 0.7); margin-bottom: 2px;">7 Tage ${weekWarning}</div>
                            <div style="font-size: 12px; color: #ffd700; font-weight: bold;">$${stats.week_cost.toFixed(4)}</div>
                            <div style="font-size: 9px; color: rgba(255, 255, 255, 0.5);">${stats.week_recordings} Aufnahmen</div>
                        </div>
                    </div>
                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 8px;">
                        <div style="background: rgba(0,0,0,0.3); padding: 8px; border-radius: 6px; text-align: center;">
                            <div style="font-size: 10px; color: rgba(255, 255, 255, 0.7); margin-bottom: 2px;">30 Tage</div>
                            <div style="font-size: 12px; color: #ffd700; font-weight: bold;">$${stats.month_cost.toFixed(4)}</div>
                            <div style="font-size: 9px; color: rgba(255, 255, 255, 0.5);">${stats.month_recordings} Aufnahmen</div>
                        </div>
                        <div style="background: rgba(0,0,0,0.3); padding: 8px; border-radius: 6px; text-align: center;">
                            <div style="font-size: 10px; color: rgba(255, 255, 255, 0.7); margin-bottom: 2px;">Gesamt</div>
                            <div style="font-size: 12px; color: #ffd700; font-weight: bold;">$${stats.total_cost.toFixed(4)}</div>
                            <div style="font-size: 9px; color: rgba(255, 255, 255, 0.5);">${stats.total_recordings} Aufnahmen</div>
                        </div>
                    </div>
                    ${stats.most_expensive_today ? `
                        <div style="background: rgba(255, 215, 0, 0.1); border: 1px solid rgba(255, 215, 0, 0.3); border-radius: 6px; padding: 8px; margin-top: 8px;">
                            <div style="font-size: 10px; color: rgba(255, 215, 0, 0.9); margin-bottom: 4px;">💎 Teuerste Aufnahme heute:</div>
                            <div style="font-size: 11px; color: rgba(255, 255, 255, 0.9);">
                                $${stats.most_expensive_today.cost_usd.toFixed(4)} • ${stats.most_expensive_today.duration_seconds.toFixed(1)}s
                            </div>
                            <div style="font-size: 10px; color: rgba(255, 255, 255, 0.7); margin-top: 2px;">
                                "${stats.most_expensive_today.transcription.substring(0, 30)}..."
                            </div>
                        </div>
                    ` : ''}
                `;

                // Warnung bei hohen Kosten
                if (stats.today_cost > 1.0) {
                    this.showToast('⚠️ Hohe Kosten heute: $' + stats.today_cost.toFixed(4));
                }
            }

            getRecordingDuration() {
                // Fallback für Recording-Dauer wenn nicht verfügbar
                return this.audioRecorder?.getRecordingDuration?.() || 0;
            }

            // State Management System
            setState(newState) {
                const oldState = { ...this.appState };
                this.appState = { ...this.appState, ...newState };

                console.log('🔄 State changed:', {
                    from: oldState,
                    to: this.appState,
                    changes: newState
                });

                // UI Updates basierend auf State-Änderungen
                this.onStateChange(oldState, this.appState);
            }

            getState() {
                return { ...this.appState };
            }

            setRecordingState(newRecordingState) {
                const oldState = this.appState.recordingState;

                // Validiere State-Übergänge
                if (!this.isValidStateTransition(oldState, newRecordingState)) {
                    console.warn(`⚠️ Invalid state transition: ${oldState} → ${newRecordingState}`);
                    return false;
                }

                this.setState({
                    recordingState: newRecordingState,
                    recordingStartTime: newRecordingState === 'RECORDING' ? Date.now() : this.appState.recordingStartTime
                });

                // Legacy compatibility
                this.isRecording = newRecordingState === 'RECORDING';

                return true;
            }

            isValidStateTransition(from, to) {
                const validTransitions = {
                    'IDLE': ['RECORDING', 'ERROR'],
                    'RECORDING': ['PROCESSING', 'ERROR', 'IDLE'],
                    'PROCESSING': ['TRANSCRIBING', 'COMPLETED', 'ERROR'],
                    'TRANSCRIBING': ['COMPLETED', 'ERROR'],
                    'COMPLETED': ['IDLE'],
                    'ERROR': ['IDLE']
                };

                return validTransitions[from]?.includes(to) || false;
            }

            onStateChange(oldState, newState) {
                // Recording State UI Updates
                if (oldState.recordingState !== newState.recordingState) {
                    this.updateRecordingStateUI(newState.recordingState);
                }

                // Panel Updates
                if (oldState.activePanel !== newState.activePanel) {
                    this.updateActivePanelIfOpen();
                }

                // Error Handling
                if (newState.lastError && oldState.lastError !== newState.lastError) {
                    this.handleStateError(newState.lastError);
                }
            }

            updateRecordingStateUI(recordingState) {
                const statusMessages = {
                    'IDLE': '⏸️ Bereit für Aufnahme',
                    'RECORDING': '🔴 Aufnahme läuft...',
                    'PROCESSING': '⚙️ Verarbeitung...',
                    'TRANSCRIBING': '🤖 Transkription läuft...',
                    'COMPLETED': '✅ Fertig',
                    'ERROR': '❌ Fehler aufgetreten'
                };

                const message = statusMessages[recordingState] || '❓ Unbekannter Status';

                // Update UI elements
                const recordingStatus = document.getElementById('recordingStatus');
                if (recordingStatus) {
                    recordingStatus.textContent = message;
                }

                // Update recording button
                const recordingBtn = document.querySelector('[onclick*="startRecording"], [onclick*="stopRecording"]');
                if (recordingBtn) {
                    const isRecording = recordingState === 'RECORDING';
                    recordingBtn.textContent = isRecording ? '🛑 Aufnahme stoppen' : '🎤 Aufnahme starten';
                    recordingBtn.onclick = isRecording ? () => this.stopRecording() : () => this.startRecording();
                    recordingBtn.disabled = ['PROCESSING', 'TRANSCRIBING'].includes(recordingState);
                }

                // Show toast for important state changes
                if (['RECORDING', 'COMPLETED', 'ERROR'].includes(recordingState)) {
                    this.showToast(message);
                }

                console.log(`🎯 Recording state UI updated: ${recordingState}`);
            }

            handleStateError(error) {
                console.error('❌ State error:', error);
                this.showToast(`❌ Fehler: ${error.message || error}`);

                // Reset to IDLE after error
                setTimeout(() => {
                    this.setRecordingState('IDLE');
                }, 2000);
            }

            // Advanced Settings Management
            updateSetting(key, value) {
                const oldValue = this.settings[key];

                // Validiere Setting
                const validation = this.validateSetting(key, value);
                if (!validation.valid) {
                    console.warn(`⚠️ Invalid setting ${key}:`, validation.error);
                    this.setState({
                        settingsErrors: { ...this.appState.settingsErrors, [key]: validation.error }
                    });
                    return false;
                }

                // Update Setting
                this.settings[key] = value;

                // State Management
                this.setState({
                    settingsChanged: true,
                    settingsErrors: { ...this.appState.settingsErrors, [key]: null }
                });

                console.log(`⚙️ Setting updated: ${key} = ${value} (was: ${oldValue})`);

                // Trigger side effects
                this.onSettingChanged(key, value, oldValue);

                // Auto-save (debounced)
                this.debouncedSaveSettings();

                return true;
            }

            validateSetting(key, value) {
                const validators = {
                    audioQuality: (v) => ['low', 'medium', 'high'].includes(v) || 'Must be low, medium, or high',
                    language: (v) => ['de', 'en', 'es', 'fr', 'it'].includes(v) || 'Unsupported language',
                    maxRecordingDuration: (v) => (v >= 10 && v <= 3600) || 'Must be between 10 and 3600 seconds',
                    costWarningThreshold: (v) => (v >= 0 && v <= 100) || 'Must be between 0 and 100 dollars',
                    openaiApiKey: (v) => (!v || v.startsWith('sk-')) || 'Must start with sk-'
                };

                const validator = validators[key];
                if (!validator) return { valid: true };

                const result = validator(value);
                return result === true ? { valid: true } : { valid: false, error: result };
            }

            onSettingChanged(key, newValue, oldValue) {
                // Handle setting-specific side effects
                switch (key) {
                    case 'openaiApiKey':
                        if (newValue && newValue !== oldValue) {
                            this.initWhisperAPI();
                        } else if (!newValue) {
                            this.whisperAPI = null;
                        }
                        break;

                    case 'language':
                        if (this.whisperAPI) {
                            this.whisperAPI.setLanguage(newValue);
                        }
                        break;

                    case 'debugMode':
                        console.log(`🐛 Debug mode ${newValue ? 'enabled' : 'disabled'}`);
                        break;

                    case 'hotkeyEnabled':
                        console.log(`⌨️ Hotkeys ${newValue ? 'enabled' : 'disabled'}`);
                        break;

                    case 'preferredAudioDevice':
                        console.log(`🎤 Preferred audio device changed to: ${newValue}`);
                        // Audio-System neu initialisieren mit neuem Gerät
                        if (this.audioRecorder) {
                            this.initAudioSystem();
                        }
                        break;

                    case 'audioQuality':
                        console.log(`🎵 Audio quality changed to: ${newValue}`);
                        // Audio-System neu initialisieren mit neuer Qualität
                        if (this.audioRecorder) {
                            this.initAudioSystem();
                        }
                        break;
                }
            }

            // Debounced save to prevent too frequent saves
            debouncedSaveSettings() {
                clearTimeout(this.saveSettingsTimeout);
                this.saveSettingsTimeout = setTimeout(() => {
                    this.saveAppSettings();
                    this.setState({ settingsChanged: false });
                }, 1000);
            }

            resetSettings() {
                if (confirm('Möchten Sie alle Einstellungen auf die Standardwerte zurücksetzen?')) {
                    this.settings = { ...this.defaultSettings };
                    this.setState({
                        settingsChanged: true,
                        settingsErrors: {}
                    });
                    this.saveAppSettings();
                    this.showToast('⚙️ Einstellungen zurückgesetzt');
                    this.updateActivePanelIfOpen();
                }
            }

            exportSettings() {
                try {
                    const exportData = {
                        version: '1.0',
                        timestamp: new Date().toISOString(),
                        settings: { ...this.settings, openaiApiKey: null } // Exclude API key for security
                    };

                    const blob = new Blob([JSON.stringify(exportData, null, 2)], { type: 'application/json' });
                    const url = URL.createObjectURL(blob);

                    const a = document.createElement('a');
                    a.href = url;
                    a.download = `meinwort-settings-${new Date().toISOString().split('T')[0]}.json`;
                    a.click();

                    URL.revokeObjectURL(url);
                    this.showToast('📤 Einstellungen exportiert');
                } catch (error) {
                    console.error('❌ Export failed:', error);
                    this.showToast('❌ Export fehlgeschlagen');
                }
            }

            async importSettings(file) {
                try {
                    const text = await file.text();
                    const importData = JSON.parse(text);

                    if (!importData.settings) {
                        throw new Error('Invalid settings file format');
                    }

                    // Validate imported settings
                    const validSettings = {};
                    for (const [key, value] of Object.entries(importData.settings)) {
                        if (key in this.defaultSettings) {
                            const validation = this.validateSetting(key, value);
                            if (validation.valid) {
                                validSettings[key] = value;
                            }
                        }
                    }

                    // Apply valid settings
                    this.settings = { ...this.settings, ...validSettings };
                    this.setState({ settingsChanged: true });
                    this.saveAppSettings();
                    this.showToast('📥 Einstellungen importiert');
                    this.updateActivePanelIfOpen();

                } catch (error) {
                    console.error('❌ Import failed:', error);
                    this.showToast('❌ Import fehlgeschlagen');
                }
            }

            // Audio Device Management
            async loadAudioDevices() {
                try {
                    console.log('🎤 Loading audio devices...');
                    const tauriInvoke = window.__TAURI__?.invoke || window.__TAURI__?.core?.invoke;

                    if (!tauriInvoke) {
                        console.warn('⚠️ Tauri not available for audio device detection');
                        return;
                    }

                    const deviceList = await tauriInvoke('get_audio_devices');
                    console.log('✅ Audio devices loaded:', deviceList);

                    this.setState({
                        availableAudioDevices: deviceList.devices,
                        currentAudioDevice: deviceList.default_device_id
                    });

                    // Update preferred device if not set
                    if (!this.settings.preferredAudioDevice && deviceList.default_device_id) {
                        this.updateSetting('preferredAudioDevice', deviceList.default_device_id);
                    }

                    return deviceList;
                } catch (error) {
                    console.error('❌ Failed to load audio devices:', error);
                    return null;
                }
            }

            async testAudioDevice(deviceId) {
                try {
                    console.log('🧪 Testing audio device:', deviceId);
                    this.setState({ audioDeviceTestInProgress: true });
                    this.showToast('🧪 Teste Mikrofon...');

                    const tauriInvoke = window.__TAURI__?.invoke || window.__TAURI__?.core?.invoke;
                    const success = await tauriInvoke('test_audio_device', { device_id: deviceId });

                    if (success) {
                        this.showToast('✅ Mikrofon-Test erfolgreich');
                    } else {
                        this.showToast('❌ Mikrofon-Test fehlgeschlagen');
                    }

                    return success;
                } catch (error) {
                    console.error('❌ Audio device test failed:', error);
                    this.showToast('❌ Mikrofon-Test fehlgeschlagen');
                    return false;
                } finally {
                    this.setState({ audioDeviceTestInProgress: false });
                }
            }

            async setPreferredAudioDevice(deviceId) {
                try {
                    console.log('🎤 Setting preferred audio device:', deviceId);
                    const tauriInvoke = window.__TAURI__?.invoke || window.__TAURI__?.core?.invoke;

                    await tauriInvoke('set_preferred_audio_device', { device_id: deviceId });

                    // Update settings
                    this.updateSetting('preferredAudioDevice', deviceId);
                    this.setState({ currentAudioDevice: deviceId });

                    this.showToast('🎤 Mikrofon geändert');
                    console.log('✅ Preferred audio device set to:', deviceId);

                } catch (error) {
                    console.error('❌ Failed to set preferred audio device:', error);
                    this.showToast('❌ Mikrofon-Änderung fehlgeschlagen');
                }
            }

            async refreshAudioDevices() {
                console.log('🔄 Refreshing audio devices...');
                await this.loadAudioDevices();
                this.updateActivePanelIfOpen();
                this.showToast('🔄 Audio-Geräte aktualisiert');
            }

            getAudioDeviceStatus(device) {
                if (!device.is_available) {
                    return { icon: '❌', text: 'Nicht verfügbar', color: 'rgba(255, 0, 0, 0.7)' };
                } else if (device.is_default) {
                    return { icon: '⭐', text: 'Standard', color: 'rgba(255, 215, 0, 0.9)' };
                } else if (device.id === this.settings.preferredAudioDevice) {
                    return { icon: '✅', text: 'Ausgewählt', color: 'rgba(0, 255, 0, 0.7)' };
                } else {
                    return { icon: '🎤', text: 'Verfügbar', color: 'rgba(255, 255, 255, 0.7)' };
                }
            }

            // AI Enhancement Functions
            async enhanceTranscription(text, options = {}) {
                try {
                    console.log('🤖 Enhancing transcription with AI...');
                    this.setState({ aiProcessingInProgress: true });
                    this.showToast('🤖 AI-Verbesserung läuft...');

                    const tauriInvoke = window.__TAURI__?.invoke || window.__TAURI__?.core?.invoke;

                    const request = {
                        text: text,
                        language: this.settings.language || 'de',
                        include_summary: options.includeSummary ?? this.settings.autoGenerateSummary,
                        include_keywords: options.includeKeywords ?? this.settings.autoExtractKeywords,
                        include_action_items: options.includeActionItems ?? this.settings.autoDetectActionItems
                    };

                    const enhancement = await tauriInvoke('enhance_transcription', request);
                    console.log('✅ AI enhancement completed:', enhancement);

                    // Speichere Enhancement
                    this.setState({
                        lastEnhancement: enhancement,
                        enhancementHistory: [enhancement, ...this.appState.enhancementHistory.slice(0, 9)] // Keep last 10
                    });

                    this.showToast(`✅ Text verbessert (${enhancement.processing_time_ms}ms)`);

                    // Update UI
                    this.updateActivePanelIfOpen();

                    return enhancement;
                } catch (error) {
                    console.error('❌ AI enhancement failed:', error);
                    this.showToast('❌ AI-Verbesserung fehlgeschlagen');
                    return null;
                } finally {
                    this.setState({ aiProcessingInProgress: false });
                }
            }

            async enhanceLastTranscription() {
                if (!this.currentTranscription?.text) {
                    this.showToast('❌ Keine Transkription zum Verbessern');
                    return;
                }

                const enhancement = await this.enhanceTranscription(this.currentTranscription.text);
                if (enhancement) {
                    // Update current transcription with enhanced version
                    this.currentTranscription = {
                        ...this.currentTranscription,
                        text: enhancement.enhanced_text,
                        enhanced: true,
                        enhancement: enhancement
                    };
                }
            }

            async copyEnhancedText(enhancement) {
                try {
                    await this.copyToClipboard(enhancement.enhanced_text);
                    this.showToast('📋 Verbesserter Text kopiert');
                } catch (error) {
                    console.error('❌ Failed to copy enhanced text:', error);
                    this.showToast('❌ Kopieren fehlgeschlagen');
                }
            }

            async copySummary(enhancement) {
                if (!enhancement.summary) {
                    this.showToast('❌ Keine Zusammenfassung verfügbar');
                    return;
                }

                try {
                    await this.copyToClipboard(enhancement.summary);
                    this.showToast('📋 Zusammenfassung kopiert');
                } catch (error) {
                    console.error('❌ Failed to copy summary:', error);
                    this.showToast('❌ Kopieren fehlgeschlagen');
                }
            }

            async copyKeywords(enhancement) {
                if (!enhancement.keywords || enhancement.keywords.length === 0) {
                    this.showToast('❌ Keine Schlüsselwörter verfügbar');
                    return;
                }

                try {
                    const keywordText = enhancement.keywords.join(', ');
                    await this.copyToClipboard(keywordText);
                    this.showToast('📋 Schlüsselwörter kopiert');
                } catch (error) {
                    console.error('❌ Failed to copy keywords:', error);
                    this.showToast('❌ Kopieren fehlgeschlagen');
                }
            }

            async copyActionItems(enhancement) {
                if (!enhancement.action_items || enhancement.action_items.length === 0) {
                    this.showToast('❌ Keine Action Items verfügbar');
                    return;
                }

                try {
                    const actionText = enhancement.action_items.join('\n');
                    await this.copyToClipboard(actionText);
                    this.showToast('📋 Action Items kopiert');
                } catch (error) {
                    console.error('❌ Failed to copy action items:', error);
                    this.showToast('❌ Kopieren fehlgeschlagen');
                }
            }

            // Workflow Automation Functions
            async loadWorkflowTemplates() {
                try {
                    console.log('⚡ Loading workflow templates...');
                    const tauriInvoke = window.__TAURI__?.invoke || window.__TAURI__?.core?.invoke;

                    if (!tauriInvoke) {
                        console.warn('⚠️ Tauri not available for workflow templates');
                        return;
                    }

                    const templates = await tauriInvoke('get_workflow_templates');
                    console.log('✅ Workflow templates loaded:', templates);

                    this.setState({ availableWorkflows: templates });

                    return templates;
                } catch (error) {
                    console.error('❌ Failed to load workflow templates:', error);
                    return [];
                }
            }

            async executeWorkflow(workflow, transcriptionData) {
                try {
                    console.log('⚡ Executing workflow:', workflow.name);
                    this.setState({ workflowExecutionInProgress: true });

                    const tauriInvoke = window.__TAURI__?.invoke || window.__TAURI__?.core?.invoke;

                    const result = await tauriInvoke('execute_workflow', {
                        workflow: workflow,
                        transcription_data: transcriptionData
                    });

                    console.log('✅ Workflow execution result:', result);

                    this.setState({ lastWorkflowExecution: result });

                    if (result.success) {
                        this.showToast(`⚡ Workflow "${workflow.name}" erfolgreich ausgeführt`);
                    } else {
                        this.showToast(`⚠️ Workflow "${workflow.name}" teilweise fehlgeschlagen`);
                    }

                    return result;
                } catch (error) {
                    console.error('❌ Workflow execution failed:', error);
                    this.showToast('❌ Workflow-Ausführung fehlgeschlagen');
                    return null;
                } finally {
                    this.setState({ workflowExecutionInProgress: false });
                }
            }

            async executeActiveWorkflows(transcriptionData) {
                if (!this.settings.enableWorkflowAutomation || !this.settings.autoExecuteWorkflows) {
                    console.log('⚡ Workflow automation disabled');
                    return;
                }

                const activeWorkflows = this.appState.availableWorkflows.filter(w => w.enabled);
                if (activeWorkflows.length === 0) {
                    console.log('⚡ No active workflows to execute');
                    return;
                }

                console.log(`⚡ Executing ${activeWorkflows.length} active workflows...`);

                for (const workflow of activeWorkflows) {
                    // Prüfe Trigger-Bedingungen
                    if (this.shouldExecuteWorkflow(workflow, transcriptionData)) {
                        await this.executeWorkflow(workflow, transcriptionData);
                    }
                }
            }

            shouldExecuteWorkflow(workflow, transcriptionData) {
                const trigger = workflow.trigger;

                switch (trigger.trigger_type) {
                    case 'always':
                        return true;

                    case 'keyword_detected':
                        if (trigger.condition && trigger.condition.keywords) {
                            const text = transcriptionData.text?.toLowerCase() || '';
                            return trigger.condition.keywords.some(keyword =>
                                text.includes(keyword.toLowerCase())
                            );
                        }
                        return false;

                    case 'cost_threshold':
                        if (trigger.condition && trigger.condition.threshold) {
                            return transcriptionData.cost >= trigger.condition.threshold;
                        }
                        return false;

                    case 'duration_threshold':
                        if (trigger.condition && trigger.condition.threshold) {
                            return transcriptionData.duration >= trigger.condition.threshold;
                        }
                        return false;

                    default:
                        return false;
                }
            }

            toggleWorkflow(workflowId) {
                const workflows = this.appState.availableWorkflows.map(w => {
                    if (w.id === workflowId) {
                        return { ...w, enabled: !w.enabled };
                    }
                    return w;
                });

                this.setState({ availableWorkflows: workflows });

                const workflow = workflows.find(w => w.id === workflowId);
                this.showToast(`⚡ Workflow "${workflow.name}" ${workflow.enabled ? 'aktiviert' : 'deaktiviert'}`);

                this.updateActivePanelIfOpen();
            }

            async testWorkflow(workflow) {
                const testData = {
                    text: "Dies ist ein Test der Workflow-Automation mit Schlüsselwörtern wie Meeting und Projekt.",
                    duration: 15.5,
                    cost: 0.002,
                    language: "de",
                    timestamp: new Date().toISOString()
                };

                this.showToast(`🧪 Teste Workflow "${workflow.name}"...`);
                await this.executeWorkflow(workflow, testData);
            }

            // Audio Processing Functions
            async loadAudioProcessingPresets() {
                try {
                    console.log('🎛️ Loading audio processing presets...');
                    const tauriInvoke = window.__TAURI__?.invoke || window.__TAURI__?.core?.invoke;

                    if (!tauriInvoke) {
                        console.warn('⚠️ Tauri not available for audio processing presets');
                        return;
                    }

                    const presets = await tauriInvoke('get_audio_processing_presets');
                    console.log('✅ Audio processing presets loaded:', presets);

                    this.setState({ audioProcessingPresets: presets });

                    return presets;
                } catch (error) {
                    console.error('❌ Failed to load audio processing presets:', error);
                    return [];
                }
            }

            async processAudioData(audioData) {
                if (!this.settings.enableAudioProcessing) {
                    console.log('🎵 Audio processing disabled');
                    return audioData;
                }

                try {
                    console.log('🎵 Processing audio data...');
                    this.setState({ audioProcessingInProgress: true });
                    this.showToast('🎵 Audio wird verarbeitet...');

                    const tauriInvoke = window.__TAURI__?.invoke || window.__TAURI__?.core?.invoke;

                    // Audio Processing Settings basierend auf Preset
                    const settings = this.getAudioProcessingSettings();

                    // Konvertiere AudioBlob zu Uint8Array
                    const audioBytes = await this.audioDataToBytes(audioData);

                    const result = await tauriInvoke('process_audio', {
                        audio_data: Array.from(audioBytes),
                        settings: settings
                    });

                    console.log('✅ Audio processing result:', result);

                    this.setState({ lastAudioProcessingResult: result });

                    this.showToast(`🎵 Audio verarbeitet: ${result.improvements_applied.length} Verbesserungen (${result.processing_time_ms}ms)`);

                    return audioData; // In echter App würde hier das verarbeitete Audio zurückgegeben
                } catch (error) {
                    console.error('❌ Audio processing failed:', error);
                    this.showToast('❌ Audio-Verarbeitung fehlgeschlagen');
                    return audioData; // Fallback auf Original
                } finally {
                    this.setState({ audioProcessingInProgress: false });
                }
            }

            getAudioProcessingSettings() {
                const presetMap = {
                    'off': {
                        noise_reduction_enabled: false,
                        noise_reduction_strength: 0.0,
                        volume_normalization_enabled: false,
                        target_volume_db: 0.0,
                        voice_enhancement_enabled: false,
                        voice_enhancement_strength: 0.0,
                        high_pass_filter_enabled: false,
                        high_pass_cutoff_hz: 0.0,
                    },
                    'light': {
                        noise_reduction_enabled: true,
                        noise_reduction_strength: 0.3,
                        volume_normalization_enabled: true,
                        target_volume_db: -15.0,
                        voice_enhancement_enabled: false,
                        voice_enhancement_strength: 0.0,
                        high_pass_filter_enabled: true,
                        high_pass_cutoff_hz: 60.0,
                    },
                    'standard': {
                        noise_reduction_enabled: true,
                        noise_reduction_strength: 0.7,
                        volume_normalization_enabled: true,
                        target_volume_db: -12.0,
                        voice_enhancement_enabled: true,
                        voice_enhancement_strength: 0.5,
                        high_pass_filter_enabled: true,
                        high_pass_cutoff_hz: 80.0,
                    },
                    'aggressive': {
                        noise_reduction_enabled: true,
                        noise_reduction_strength: 0.9,
                        volume_normalization_enabled: true,
                        target_volume_db: -10.0,
                        voice_enhancement_enabled: true,
                        voice_enhancement_strength: 0.8,
                        high_pass_filter_enabled: true,
                        high_pass_cutoff_hz: 100.0,
                    }
                };

                return presetMap[this.settings.audioProcessingPreset] || presetMap['standard'];
            }

            async audioDataToBytes(audioData) {
                // Konvertiere AudioBlob zu Uint8Array
                if (audioData instanceof Blob) {
                    const arrayBuffer = await audioData.arrayBuffer();
                    return new Uint8Array(arrayBuffer);
                } else if (audioData instanceof ArrayBuffer) {
                    return new Uint8Array(audioData);
                } else if (audioData instanceof Uint8Array) {
                    return audioData;
                } else {
                    // Fallback: Erstelle dummy Audio-Daten
                    return new Uint8Array(1024);
                }
            }

            getQualityScoreColor(score) {
                if (score >= 0.8) return 'rgba(0, 255, 0, 0.9)';
                if (score >= 0.6) return 'rgba(255, 215, 0, 0.9)';
                if (score >= 0.4) return 'rgba(255, 165, 0, 0.9)';
                return 'rgba(255, 0, 0, 0.9)';
            }

            getQualityScoreText(score) {
                if (score >= 0.8) return 'Ausgezeichnet';
                if (score >= 0.6) return 'Gut';
                if (score >= 0.4) return 'Mittelmäßig';
                return 'Schlecht';
            }

            // Einstellungen-Management
            async loadAppSettings() {
                try {
                    console.log('📋 Loading app settings...');
                    const tauriInvoke = window.__TAURI__?.invoke || window.__TAURI__?.core?.invoke;

                    if (!tauriInvoke) {
                        console.warn('⚠️ Tauri not available, using default settings');
                        return;
                    }

                    const settings = await tauriInvoke('load_settings');
                    console.log('✅ Settings loaded:', settings);

                    // API-Key setzen wenn verfügbar
                    if (settings.openai_api_key) {
                        this.settings.openaiApiKey = settings.openai_api_key;
                        this.initWhisperAPI();
                        console.log('🔑 API-Key loaded from settings');
                    }

                    // Andere Einstellungen übernehmen (mit Fallbacks)
                    this.settings.autoClipboard = settings.auto_clipboard ?? this.defaultSettings.autoClipboard;
                    this.settings.language = settings.language || this.defaultSettings.language;
                    this.settings.audioQuality = settings.audio_quality || this.defaultSettings.audioQuality;
                    this.settings.maxRecordingDuration = settings.max_recording_duration || this.defaultSettings.maxRecordingDuration;
                    this.settings.showNotifications = settings.show_notifications ?? this.defaultSettings.showNotifications;
                    this.settings.autoSaveRecordings = settings.auto_save_recordings ?? this.defaultSettings.autoSaveRecordings;
                    this.settings.costWarningThreshold = settings.cost_warning_threshold || this.defaultSettings.costWarningThreshold;
                    this.settings.debugMode = settings.debug_mode ?? this.defaultSettings.debugMode;
                    this.settings.hotkeyEnabled = settings.hotkey_enabled ?? this.defaultSettings.hotkeyEnabled;

                } catch (error) {
                    console.error('❌ Failed to load settings:', error);
                }
            }

            async saveAppSettings() {
                try {
                    console.log('💾 Saving app settings...');
                    const tauriInvoke = window.__TAURI__?.invoke || window.__TAURI__?.core?.invoke;

                    if (!tauriInvoke) {
                        console.warn('⚠️ Tauri not available, cannot save settings');
                        return;
                    }

                    const settings = {
                        openai_api_key: this.settings.openaiApiKey || null,
                        auto_clipboard: this.settings.autoClipboard,
                        language: this.settings.language || 'de',
                        audio_quality: this.settings.audioQuality,
                        max_recording_duration: this.settings.maxRecordingDuration,
                        show_notifications: this.settings.showNotifications,
                        auto_save_recordings: this.settings.autoSaveRecordings,
                        cost_warning_threshold: this.settings.costWarningThreshold,
                        debug_mode: this.settings.debugMode,
                        hotkey_enabled: this.settings.hotkeyEnabled
                    };

                    await tauriInvoke('save_settings', settings);
                    console.log('✅ Settings saved');
                    this.showToast('💾 Einstellungen gespeichert');

                } catch (error) {
                    console.error('❌ Failed to save settings:', error);
                    this.showToast('❌ Speichern fehlgeschlagen');
                }
            }



            // API-Key Management
            setOpenAIApiKey(apiKey) {
                if (!apiKey) return false;
                
                // Validierung
                const validation = WhisperAPI.validateApiKeyFormat(apiKey);
                if (!validation.valid) {
                    console.warn('⚠️ Invalid API key format:', validation.message);
                    this.showToast('❌ ' + validation.message);
                    return false;
                }
                
                this.settings.openaiApiKey = apiKey;
                
                // WhisperAPI neu initialisieren
                if (typeof WhisperAPI !== 'undefined') {
                    this.whisperAPI = new WhisperAPI(apiKey);
                    console.log('✅ WhisperAPI reinitialized with new API key');
                    this.showToast('✅ API-Key gesetzt und gespeichert');

                    // Einstellungen automatisch speichern
                    this.saveAppSettings();

                    return true;
                } else {
                    console.warn('⚠️ WhisperAPI class not available');
                    this.showToast('⚠️ WhisperAPI nicht verfügbar');
                    return false;
                }
            }

            async testWhisperConnection() {
                if (!this.whisperAPI) {
                    this.showToast('❌ Kein API-Key gesetzt');
                    return false;
                }
                
                try {
                    this.showToast('🔄 Teste API-Verbindung...');
                    console.log('🔍 Testing OpenAI connection with API key:', this.whisperAPI.apiKey?.substring(0, 8) + '...');
                    
                    const result = await this.whisperAPI.testConnection();
                    console.log('🔍 API test result:', result);
                    
                    this.showToast(result.message);
                    return result.success;
                } catch (error) {
                    console.error('❌ API test failed with error:', error);
                    this.showToast('❌ Verbindungstest fehlgeschlagen: ' + error.message);
                    return false;
                }
            }

            copyLastTranscription() {
                if (this.currentTranscription && this.currentTranscription.text) {
                    this.copyToClipboard(this.currentTranscription.text);
                    this.showToast('📋 Text in Zwischenablage kopiert');
                } else {
                    this.showToast('❌ Keine Transkription verfügbar');
                }
            }

            setApiKeyFromInput() {
                const input = document.getElementById('apiKeyInput');
                if (input && input.value.trim()) {
                    const success = this.setOpenAIApiKey(input.value.trim());
                    if (success) {
                        input.value = ''; // Input leeren nach erfolgreichem Setzen
                        this.updateActivePanelIfOpen();
                    }
                } else {
                    this.showToast('❌ Bitte geben Sie einen API-Key ein');
                }
            }

            clearApiKey() {
                if (confirm('Möchten Sie den gespeicherten API-Key wirklich löschen?')) {
                    this.settings.openaiApiKey = null;
                    this.whisperAPI = null;
                    this.saveAppSettings();
                    this.showToast('🗑️ API-Key gelöscht');
                    this.updateActivePanelIfOpen();
                }
            }

            updateActivePanelIfOpen() {
                // Panel aktualisieren falls AI+ Panel geöffnet ist
                const panel = document.getElementById('expandablePanel');
                const panelTitle = document.getElementById('panelTitle');
                
                if (panel && panel.classList.contains('active') && panelTitle) {
                    // Prüfen ob AI+ Panel aktiv ist
                    if (panelTitle.textContent.includes('AI+')) {
                        this.loadPanelContent('ai', panelTitle, document.getElementById('panelContent'));
                    }
                }
            }

            // Real-time Audio Feedback für Visualizer
            startAudioFeedback() {
                if (!this.audioRecorder || !this.audioRecorder.audioStream) {
                    console.warn('⚠️ No audio stream available for real-time feedback');
                    return;
                }

                try {
                    // Audio-Stream für Analyse verbinden
                    if (this.audioContext.state === 'suspended') {
                        this.audioContext.resume();
                    }

                    this.audioSource = this.audioContext.createMediaStreamSource(this.audioRecorder.audioStream);
                    this.audioSource.connect(this.audioAnalyzer);
                    
                    // Real-time Analysis starten
                    this.startAudioAnalysis();
                    
                    console.log('✅ Real-time audio feedback started');
                } catch (error) {
                    console.error('❌ Failed to start audio feedback:', error);
                }
            }

            stopAudioFeedback() {
                // Audio-Analysis stoppen
                this.stopAudioAnalysis();
                
                // Audio-Source trennen
                if (this.audioSource) {
                    this.audioSource.disconnect();
                    this.audioSource = null;
                }
                
                console.log('✅ Real-time audio feedback stopped');
            }

            startAudioAnalysis() {
                if (this.audioAnalysisRunning) return;
                
                this.audioAnalysisRunning = true;
                
                const analyzeAudio = () => {
                    if (!this.audioAnalysisRunning || !this.isRecording) return;
                    
                    // Frequenz-Daten abrufen
                    this.audioAnalyzer.getByteFrequencyData(this.audioData);
                    
                    // Audio-Daten verarbeiten
                    const audioInfo = this.processAudioData(this.audioData);
                    
                    // An Visualizer weiterleiten
                    if (this.visualizer && this.visualizer.updateAudioData) {
                        this.visualizer.updateAudioData(audioInfo);
                    }
                    
                    // Nächsten Frame planen
                    requestAnimationFrame(analyzeAudio);
                };
                
                analyzeAudio();
                console.log('✅ Audio analysis loop started');
            }

            stopAudioAnalysis() {
                this.audioAnalysisRunning = false;
                console.log('✅ Audio analysis loop stopped');
            }

            processAudioData(dataArray) {
                const length = dataArray.length;
                
                // Frequenz-Bänder berechnen
                const bassRange = Math.floor(length * 0.1);   // 0-10%
                const midRange = Math.floor(length * 0.5);    // 10-50%
                const highRange = length;                     // 50-100%
                
                let bassTotal = 0;
                let midTotal = 0;
                let highTotal = 0;
                let overallTotal = 0;
                
                // Bass (tiefe Frequenzen)
                for (let i = 0; i < bassRange; i++) {
                    bassTotal += dataArray[i];
                }
                
                // Mitten (mittlere Frequenzen)
                for (let i = bassRange; i < midRange; i++) {
                    midTotal += dataArray[i];
                }
                
                // Höhen (hohe Frequenzen)
                for (let i = midRange; i < highRange; i++) {
                    highTotal += dataArray[i];
                }
                
                // Gesamt-Volumen
                for (let i = 0; i < length; i++) {
                    overallTotal += dataArray[i];
                }
                
                // Normalisieren (0-1)
                const normalize = (value, range) => Math.min(value / (255 * range), 1);
                
                return {
                    volume: normalize(overallTotal, length),
                    low: normalize(bassTotal, bassRange),
                    mid: normalize(midTotal, midRange - bassRange),
                    high: normalize(highTotal, highRange - midRange),
                    raw: dataArray
                };
            }
            
            showToast(message, duration = 3000) {
                console.log('📢 Toast:', message);
                const toast = document.createElement('div');
                toast.style.cssText = `
                    position: fixed;
                    top: 20px;
                    left: 50%;
                    transform: translateX(-50%);
                    background: rgba(0, 0, 0, 0.9);
                    color: white;
                    padding: 12px 24px;
                    border-radius: 8px;
                    font-size: 13px;
                    font-weight: 500;
                    z-index: 1000;
                    pointer-events: none;
                    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.5);
                    border: 1px solid rgba(255, 255, 255, 0.2);
                    backdrop-filter: blur(10px);
                    max-width: 400px;
                    text-align: center;
                `;
                toast.textContent = message;
                document.body.appendChild(toast);
                
                setTimeout(() => {
                    if (document.body.contains(toast)) {
                        document.body.removeChild(toast);
                    }
                }, duration);
            }
        }

        // Global function for closing expandable panel (accessible from HTML)
        window.closeExpandablePanel = async function() {
            const app = document.getElementById('app');
            const panel = document.getElementById('expandablePanel');
            
            // Tauri-Fenster zurück auf 200x200
            if (window.tauriReady && window.__TAURI__ && window.__TAURI__.window) {
                try {
                    const appWindow = window.__TAURI__.window.getCurrentWindow();
                    await appWindow.setSize(new window.__TAURI__.window.LogicalSize(200, 200));
                    console.log('✅ Tauri window resized back to 200x200');
                } catch (error) {
                    console.log('❌ Failed to resize Tauri window back:', error);
                }
            }
            
            app.classList.remove('expanded');
            panel.classList.remove('active');
            
            console.log('📋 Expandable panel closed');
        }
        
        // App starten
        console.log('🚀 Starting MeinWort App...');
        const app = new MeinWortApp();
        window.app = app; // Global zugänglich machen für onclick handlers
        console.log('✅ MeinWort App initialized');
    </script>
</body>
</html>