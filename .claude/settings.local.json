{"permissions": {"allow": ["<PERSON><PERSON>(mkdir:*)", "Bash(npm init:*)", "Bash(npm run dev:*)", "Bash(ls:*)", "Bash(npm run tauri:*)", "Bash(/Users/<USER>/.claude/local/node_modules/@anthropic-ai/claude-code/vendor/ripgrep/arm64-darwin/rg -n -A 10 \"audioDataToBytes\" index.html)", "Bash(/Users/<USER>/.claude/local/node_modules/@anthropic-ai/claude-code/vendor/ripgrep/arm64-darwin/rg -n -B 5 -A 15 \"enableAudioProcessing\" index.html)", "Bash(/Users/<USER>/.claude/local/node_modules/@anthropic-ai/claude-code/vendor/ripgrep/arm64-darwin/rg -n -A 3 \"fn process_audio\" src-tauri/src/main.rs)"], "deny": []}}